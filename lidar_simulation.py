import numpy as np
import open3d as o3d
import math
from typing import <PERSON>ple, List
import os


class LidarSimulator:
    """激光雷达点云投影仿真器"""
    
    def __init__(self, 
                 position: Tuple[float, float, float] = (0, 0, 0),
                 direction: Tuple[float, float, float] = (1, 0, 0),
                 horizontal_fov: float = 360.0,
                 vertical_fov: float = 30.0,
                 horizontal_resolution: float = 0.1,
                 vertical_resolution: float = 0.1,
                 max_range: float = 100.0):
        """
        初始化激光雷达仿真器
        
        Args:
            position: 激光雷达位置 (x, y, z)
            direction: 激光雷达朝向方向向量 (x, y, z)
            horizontal_fov: 水平视场角 (度)
            vertical_fov: 垂直视场角 (度)
            horizontal_resolution: 水平角分辨率 (度)
            vertical_resolution: 垂直角分辨率 (度)
            max_range: 最大探测距离
        """
        self.position = np.array(position)
        self.direction = np.array(direction) / np.linalg.norm(direction)
        self.horizontal_fov = horizontal_fov
        self.vertical_fov = vertical_fov
        self.horizontal_resolution = horizontal_resolution
        self.vertical_resolution = vertical_resolution
        self.max_range = max_range
        
        # 计算扫描角度
        self.horizontal_angles = np.arange(
            -horizontal_fov/2, horizontal_fov/2 + horizontal_resolution, 
            horizontal_resolution
        )
        self.vertical_angles = np.arange(
            -vertical_fov/2, vertical_fov/2 + vertical_resolution, 
            vertical_resolution
        )
        
        print(f"激光雷达配置:")
        print(f"  位置: {self.position}")
        print(f"  方向: {self.direction}")
        print(f"  水平FOV: {horizontal_fov}°, 分辨率: {horizontal_resolution}°")
        print(f"  垂直FOV: {vertical_fov}°, 分辨率: {vertical_resolution}°")
        print(f"  最大距离: {max_range}m")
        print(f"  总射线数: {len(self.horizontal_angles) * len(self.vertical_angles)}")
    
    def load_glb_model(self, glb_path: str) -> o3d.geometry.TriangleMesh:
        """加载GLB模型"""
        if not os.path.exists(glb_path):
            raise FileNotFoundError(f"GLB文件不存在: {glb_path}")
        
        print(f"加载GLB模型: {glb_path}")
        mesh = o3d.io.read_triangle_mesh(glb_path)
        
        if len(mesh.vertices) == 0:
            raise ValueError("GLB模型加载失败或为空")
        
        print(f"模型顶点数: {len(mesh.vertices)}")
        print(f"模型三角面数: {len(mesh.triangles)}")
        
        # 确保模型有法向量
        if not mesh.has_vertex_normals():
            mesh.compute_vertex_normals()
        
        return mesh
    
    def _compute_rotation_matrix(self) -> np.ndarray:
        """计算从标准方向到目标方向的旋转矩阵"""
        # 标准方向为 (1, 0, 0)
        standard_dir = np.array([1, 0, 0])
        target_dir = self.direction
        
        # 如果方向已经是标准方向，返回单位矩阵
        if np.allclose(standard_dir, target_dir):
            return np.eye(3)
        
        # 如果方向相反，绕y轴旋转180度
        if np.allclose(standard_dir, -target_dir):
            return np.array([[-1, 0, 0], [0, 1, 0], [0, 0, -1]])
        
        # 计算旋转轴和角度
        axis = np.cross(standard_dir, target_dir)
        axis = axis / np.linalg.norm(axis)
        angle = np.arccos(np.clip(np.dot(standard_dir, target_dir), -1, 1))
        
        # 使用罗德里格斯公式计算旋转矩阵
        K = np.array([[0, -axis[2], axis[1]],
                      [axis[2], 0, -axis[0]],
                      [-axis[1], axis[0], 0]])
        
        R = np.eye(3) + np.sin(angle) * K + (1 - np.cos(angle)) * np.dot(K, K)
        return R
    
    def generate_rays(self) -> Tuple[np.ndarray, np.ndarray]:
        """生成激光射线的起点和方向"""
        rotation_matrix = self._compute_rotation_matrix()
        
        rays_origin = []
        rays_direction = []
        
        for h_angle in self.horizontal_angles:
            for v_angle in self.vertical_angles:
                # 转换为弧度
                h_rad = np.radians(h_angle)
                v_rad = np.radians(v_angle)
                
                # 在标准坐标系中计算方向 (x轴为前方)
                x = np.cos(v_rad) * np.cos(h_rad)
                y = np.cos(v_rad) * np.sin(h_rad)
                z = np.sin(v_rad)
                
                direction = np.array([x, y, z])
                
                # 应用旋转矩阵
                rotated_direction = rotation_matrix @ direction
                
                rays_origin.append(self.position)
                rays_direction.append(rotated_direction)
        
        return np.array(rays_origin), np.array(rays_direction)
    
    def simulate_lidar_scan(self, mesh: o3d.geometry.TriangleMesh) -> o3d.geometry.PointCloud:
        """执行激光雷达扫描仿真"""
        print("开始激光雷达扫描仿真...")
        
        # 创建射线投射场景
        scene = o3d.t.geometry.RaycastingScene()
        mesh_t = o3d.t.geometry.TriangleMesh.from_legacy(mesh)
        scene.add_triangles(mesh_t)
        
        # 生成射线
        rays_origin, rays_direction = self.generate_rays()
        
        # 创建射线张量
        rays = o3d.core.Tensor(
            np.concatenate([rays_origin, rays_direction], axis=1),
            dtype=o3d.core.Dtype.Float32
        )
        
        print(f"投射 {len(rays)} 条射线...")
        
        # 执行射线投射
        result = scene.cast_rays(rays)
        
        # 提取命中点
        hit_distances = result['t_hit'].numpy()
        hit_points = []
        
        for i, (origin, direction, distance) in enumerate(zip(rays_origin, rays_direction, hit_distances)):
            if distance < self.max_range and not np.isinf(distance):
                hit_point = origin + direction * distance
                hit_points.append(hit_point)
        
        print(f"命中点数: {len(hit_points)}")
        
        # 创建点云
        if len(hit_points) > 0:
            point_cloud = o3d.geometry.PointCloud()
            point_cloud.points = o3d.utility.Vector3dVector(np.array(hit_points))
            
            # 为点云着色（可选）
            colors = np.tile([0.8, 0.2, 0.2], (len(hit_points), 1))  # 红色
            point_cloud.colors = o3d.utility.Vector3dVector(colors)
            
            return point_cloud
        else:
            print("警告: 没有检测到任何命中点")
            return o3d.geometry.PointCloud()
    
    def save_point_cloud(self, point_cloud: o3d.geometry.PointCloud, filename: str):
        """保存点云为PCD文件"""
        if len(point_cloud.points) == 0:
            print("警告: 点云为空，无法保存")
            return
        
        success = o3d.io.write_point_cloud(filename, point_cloud)
        if success:
            print(f"点云已保存到: {filename}")
        else:
            print(f"保存点云失败: {filename}")
    
    def visualize(self, mesh: o3d.geometry.TriangleMesh, point_cloud: o3d.geometry.PointCloud):
        """可视化模型和点云"""
        print("启动可视化...")
        
        # 创建可视化对象列表
        vis_objects = []
        
        # 添加原始模型（半透明）
        import copy
        mesh_copy = copy.deepcopy(mesh)
        mesh_copy.paint_uniform_color([0.7, 0.7, 0.7])
        vis_objects.append(mesh_copy)
        
        # 添加点云
        if len(point_cloud.points) > 0:
            vis_objects.append(point_cloud)
        
        # 添加激光雷达位置标记
        lidar_marker = o3d.geometry.TriangleMesh.create_sphere(radius=0.5)
        lidar_marker.translate(self.position)
        lidar_marker.paint_uniform_color([0, 1, 0])  # 绿色
        vis_objects.append(lidar_marker)
        
        # 添加方向指示器
        direction_end = self.position + self.direction * 5
        direction_points = [self.position, direction_end]
        direction_lines = [[0, 1]]
        direction_line_set = o3d.geometry.LineSet()
        direction_line_set.points = o3d.utility.Vector3dVector(direction_points)
        direction_line_set.lines = o3d.utility.Vector2iVector(direction_lines)
        direction_line_set.colors = o3d.utility.Vector3dVector([[0, 1, 0]])  # 绿色
        vis_objects.append(direction_line_set)
        
        # 显示可视化
        o3d.visualization.draw_geometries(
            vis_objects,
            window_name="激光雷达点云仿真",
            width=1200,
            height=800
        )


def create_example_scene():
    """创建示例场景"""
    print("创建示例场景...")

    # 创建地面
    ground = o3d.geometry.TriangleMesh.create_box(width=20, height=20, depth=0.1)
    ground.translate([-10, -10, -0.1])
    ground.paint_uniform_color([0.5, 0.5, 0.5])

    # 创建建筑物
    building1 = o3d.geometry.TriangleMesh.create_box(width=3, height=3, depth=5)
    building1.translate([2, 2, 0])
    building1.paint_uniform_color([0.8, 0.4, 0.4])

    building2 = o3d.geometry.TriangleMesh.create_box(width=2, height=4, depth=3)
    building2.translate([-6, 1, 0])
    building2.paint_uniform_color([0.4, 0.8, 0.4])

    # 创建圆柱体
    cylinder = o3d.geometry.TriangleMesh.create_cylinder(radius=0.5, height=2)
    cylinder.translate([0, -5, 0])
    cylinder.paint_uniform_color([0.4, 0.4, 0.8])

    # 合并所有几何体
    scene_mesh = ground + building1 + building2 + cylinder
    scene_mesh.compute_vertex_normals()

    return scene_mesh


def main():
    """主函数示例"""
    # 创建激光雷达仿真器
    lidar = LidarSimulator(
        position=(0, 0, 8),           # 激光雷达位置
        direction=(0, 0, -1),         # 向下扫描
        horizontal_fov=180.0,         # 水平180度
        vertical_fov=45.0,            # 垂直45度
        horizontal_resolution=2.0,    # 水平2度分辨率
        vertical_resolution=2.0,      # 垂直2度分辨率
        max_range=30.0               # 最大30米
    )

    # 尝试加载GLB文件，如果不存在则创建示例场景
    glb_path = "example_model.glb"
    if os.path.exists(glb_path):
        print(f"加载GLB文件: {glb_path}")
        mesh = lidar.load_glb_model(glb_path)
    else:
        print("未找到GLB文件，创建示例场景...")
        mesh = create_example_scene()

    # 执行激光雷达扫描
    point_cloud = lidar.simulate_lidar_scan(mesh)

    # 保存点云
    lidar.save_point_cloud(point_cloud, "lidar_scan.pcd")

    # 可视化结果
    lidar.visualize(mesh, point_cloud)


if __name__ == "__main__":
    main()
