#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
20 辆车 + 20 台 LiDAR —— 线程版（无 pickle 问题）
python fast_fleet_thread.py car.glb --start 0 0 8 --end 0 0 18
"""
import os, time, numpy as np, open3d as o3d
from concurrent.futures import ThreadPoolExecutor
from open3d.t.geometry import RaycastingScene

# ---------- 全局参数 ----------
N_CARS = 20
H_FOV, V_FOV = 180.0, 45.0
H_RES, V_RES = 0.2, 0.2
MAX_RANGE = 30.0
OUT_DIR = "fleet_output"

# ---------- 预生成光线 ----------
def _build_dirs():
    h = np.deg2rad(np.arange(-H_FOV/2, H_FOV/2+1e-6, H_RES))
    v = np.deg2rad(np.arange(-V_FOV/2, V_FOV/2+1e-6, V_RES))
    hh, vv = np.meshgrid(h, v)
    hh, vv = hh.ravel(), vv.ravel()
    dirs = np.stack([
        np.cos(vv) * np.sin(hh),
        np.sin(vv),
        -np.cos(vv) * np.cos(hh)
    ], -1).astype(np.float32)
    return dirs
DIRS = _build_dirs()
N_DIR = len(DIRS)

# ---------- 1. 逐车建 BVH（线程池，无 pickle 限制） ----------
def build_scene_for_car(args):
    path, idx = args
    mesh = o3d.io.read_triangle_mesh(path)
    if not mesh.has_vertices():
        raise RuntimeError(f"{path} empty")
    mesh.scale(0.01, center=(0, 0, 0))
    mesh.translate([idx * 4, 0, 0])
    mesh.compute_vertex_normals()

    scene = RaycastingScene()
    scene.add_triangles(o3d.t.geometry.TriangleMesh.from_legacy(mesh))
    return scene

def build_all_scenes(car_path):
    with ThreadPoolExecutor(max_workers=min(N_CARS, os.cpu_count())) as exe:
        scenes = list(exe.map(build_scene_for_car,
                              [(car_path, i) for i in range(N_CARS)]))
    return scenes

# ---------- 2. 单 LiDAR 扫描 ----------
def lidar_scan(args):
    scene, origin = args
    origins = np.full_like(DIRS, origin, dtype=np.float32)
    rays = np.hstack([origins, DIRS]).astype(np.float32)

    ans = scene.cast_rays(rays)
    t_hit = ans['t_hit'].numpy()
    mask = (t_hit > 0) & (t_hit < MAX_RANGE)
    pts = origins[mask] + DIRS[mask] * t_hit[mask, None]

    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(pts)
    return pc

# ---------- 3. 并行扫描 ----------
def scan_frame_parallel(scenes, lidar_poses):
    with ThreadPoolExecutor(max_workers=N_CARS) as exe:
        clouds = list(exe.map(lidar_scan, zip(scenes, lidar_poses)))
    return clouds

# ---------- 4. 主流程 ----------
def run_fleet(car_glb_path,
              start_pos=(0,0,8),
              end_pos=(0,0,18)):
    import os
    os.makedirs(OUT_DIR, exist_ok=True)

    print("Loading & building 20 BVH (threaded) ...")
    t0 = time.perf_counter()
    scenes = build_all_scenes(car_glb_path)
    t1 = time.perf_counter()

    lidar_poses = np.linspace(start_pos, end_pos, N_CARS)

    print("Scanning 20 LiDAR ...")
    clouds = scan_frame_parallel(scenes, lidar_poses)
    t2 = time.perf_counter()

    print("Saving 20 clouds ...")
    for i, cloud in enumerate(clouds):
        path = f"{OUT_DIR}/car_{i:02d}.pcd"
        o3d.io.write_point_cloud(path, cloud, print_progress=False)
    t3 = time.perf_counter()

    total_pts = sum(len(c.points) for c in clouds)
    print(f"Total time {t3-t0:.2f}s "
          f"(BVH {t1-t0:.2f}s, scan {t2-t1:.2f}s, save {t3-t2:.2f}s) "
          f"Points {total_pts}")

# ---------- CLI ----------
if __name__ == "__main__":
    import argparse, os
    parser = argparse.ArgumentParser()
    parser.add_argument("car_glb")
    parser.add_argument("--start", nargs=3, type=float, default=[0,0,8])
    parser.add_argument("--end",   nargs=3, type=float, default=[0,0,18])
    args = parser.parse_args()
    run_fleet(args.car_glb, tuple(args.start), tuple(args.end))