# 激光雷达点云投影仿真系统

基于Open3D的激光雷达点云投影仿真系统，支持GLB模型导入、自定义激光传感器参数、点云生成和可视化。

## 功能特性

- 🎯 **GLB模型支持**: 导入和处理GLB 3D模型
- 📡 **激光雷达仿真**: 可配置的激光传感器参数
- 🎨 **实时可视化**: 3D场景和点云可视化
- 💾 **点云保存**: 支持PCD格式点云文件保存
- ⚙️ **灵活配置**: 自定义传感器位置、方向、视场角等参数

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 基本使用

```python
from lidar_simulation import LidarSimulator

# 创建激光雷达仿真器
lidar = LidarSimulator(
    position=(0, 0, 10),          # 传感器位置 (x, y, z)
    direction=(0, 0, -1),         # 朝向方向 (x, y, z)
    horizontal_fov=360.0,         # 水平视场角 (度)
    vertical_fov=30.0,            # 垂直视场角 (度)
    horizontal_resolution=1.0,    # 水平角分辨率 (度)
    vertical_resolution=1.0,      # 垂直角分辨率 (度)
    max_range=50.0               # 最大探测距离 (米)
)

# 加载GLB模型
mesh = lidar.load_glb_model("your_model.glb")

# 执行激光雷达扫描
point_cloud = lidar.simulate_lidar_scan(mesh)

# 保存点云
lidar.save_point_cloud(point_cloud, "scan_result.pcd")

# 可视化结果
lidar.visualize(mesh, point_cloud)
```

### 2. 运行示例

```bash
# 运行基本示例（使用内置测试场景）
python lidar_simulation.py

# 运行多种扫描示例
python example_usage.py
```

## 参数说明

### LidarSimulator 参数

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `position` | tuple | 激光雷达位置坐标 (x, y, z) | (0, 0, 0) |
| `direction` | tuple | 激光雷达朝向方向向量 (x, y, z) | (1, 0, 0) |
| `horizontal_fov` | float | 水平视场角 (度) | 360.0 |
| `vertical_fov` | float | 垂直视场角 (度) | 30.0 |
| `horizontal_resolution` | float | 水平角分辨率 (度) | 0.1 |
| `vertical_resolution` | float | 垂直角分辨率 (度) | 0.1 |
| `max_range` | float | 最大探测距离 (米) | 100.0 |

### 坐标系说明

- **X轴**: 向右为正
- **Y轴**: 向前为正  
- **Z轴**: 向上为正
- **方向向量**: 单位向量，表示激光雷达的主要扫描方向

## 使用示例

### 示例1: 俯视扫描
```python
lidar = LidarSimulator(
    position=(0, 0, 15),      # 15米高度
    direction=(0, 0, -1),     # 向下扫描
    horizontal_fov=360.0,     # 全方位扫描
    vertical_fov=60.0,        # 60度垂直角
    horizontal_resolution=2.0,
    vertical_resolution=2.0
)
```

### 示例2: 侧面扫描
```python
lidar = LidarSimulator(
    position=(-30, 0, 5),     # 侧面位置
    direction=(1, 0, 0),      # 向前扫描
    horizontal_fov=120.0,     # 120度水平角
    vertical_fov=45.0,        # 45度垂直角
    horizontal_resolution=1.0,
    vertical_resolution=1.0
)
```

### 示例3: 高密度扫描
```python
lidar = LidarSimulator(
    position=(0, 0, 8),
    direction=(0, 0, -1),
    horizontal_fov=180.0,
    vertical_fov=30.0,
    horizontal_resolution=0.5,  # 高分辨率
    vertical_resolution=0.5,    # 高分辨率
    max_range=25.0
)
```

## 输出文件

- **PCD文件**: 点云数据，可用CloudCompare、PCL等工具打开
- **可视化窗口**: 实时3D显示，包含原始模型和生成的点云

## 性能优化建议

1. **分辨率设置**: 降低角分辨率可显著提高性能
2. **视场角限制**: 减小FOV可减少射线数量
3. **距离限制**: 合理设置max_range避免无效计算
4. **模型优化**: 使用简化的GLB模型可提高射线投射速度

## 故障排除

### 常见问题

1. **GLB文件加载失败**
   - 检查文件路径是否正确
   - 确认GLB文件格式有效
   - 尝试使用其他3D软件验证模型

2. **点云为空**
   - 检查激光雷达位置是否合理
   - 调整max_range参数
   - 验证模型是否在扫描范围内

3. **性能问题**
   - 降低角分辨率
   - 减小视场角
   - 使用更简单的模型

4. **可视化问题**
   - 确保Open3D正确安装
   - 检查显卡驱动
   - 尝试不同的可视化参数

## 技术细节

- **射线投射**: 使用Open3D的RaycastingScene进行高效射线投射
- **坐标变换**: 支持任意方向的激光雷达朝向
- **点云格式**: 标准PCD格式，兼容主流点云处理工具
- **可视化**: 基于Open3D的3D可视化引擎

## 扩展功能

可以基于此系统扩展的功能：
- 多传感器融合仿真
- 动态场景仿真
- 噪声模型添加
- 不同激光雷达类型仿真
- 批量场景处理

## 许可证

MIT License
