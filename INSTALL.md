# 安装和使用指南

## 系统要求

- Python 3.7 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux
- 至少 4GB RAM（推荐 8GB+）
- 支持 OpenGL 的显卡

## 安装步骤

### 1. 克隆或下载项目

```bash
# 如果使用 git
git clone <repository-url>
cd trun_cloud

# 或者直接下载并解压到目录
```

### 2. 安装 Python 依赖

```bash
# 使用 pip 安装
pip install -r requirements.txt

# 或者单独安装
pip install open3d>=0.17.0 numpy>=1.21.0 matplotlib>=3.5.0
```

### 3. 验证安装

```bash
# 运行系统测试
python test_system.py
```

如果看到 "🎉 所有测试通过！系统运行正常"，说明安装成功。

## 快速开始

### 方法1: 使用预设配置

```bash
# 查看所有可用的预设配置
python run_simulation.py --list-presets

# 使用俯视扫描预设
python run_simulation.py --preset overhead_scan

# 使用汽车激光雷达预设
python run_simulation.py --preset automotive
```

### 方法2: 直接运行示例

```bash
# 运行基本示例
python lidar_simulation.py

# 运行多种扫描示例
python example_usage.py
```

### 方法3: 自定义参数

```bash
# 自定义激光雷达参数
python run_simulation.py \
    --position 0 0 10 \
    --direction 0 0 -1 \
    --horizontal-fov 180 \
    --vertical-fov 45 \
    --horizontal-resolution 1.0 \
    --vertical-resolution 1.0 \
    --max-range 30
```

## 使用 GLB 模型

### 1. 准备 GLB 文件

将您的 GLB 文件放在项目目录中，例如 `my_model.glb`

### 2. 运行仿真

```bash
# 使用 GLB 模型
python run_simulation.py --preset overhead_scan --model my_model.glb

# 或者在代码中指定
python -c "
from lidar_simulation import LidarSimulator
lidar = LidarSimulator(position=(0,0,10), direction=(0,0,-1))
mesh = lidar.load_glb_model('my_model.glb')
pc = lidar.simulate_lidar_scan(mesh)
lidar.save_point_cloud(pc, 'my_scan.pcd')
lidar.visualize(mesh, pc)
"
```

## 常用命令

### 查看帮助

```bash
python run_simulation.py --help
```

### 不显示可视化（适合批处理）

```bash
python run_simulation.py --preset quick_test --no-visualization
```

### 指定输出目录和文件名

```bash
python run_simulation.py \
    --preset overhead_scan \
    --output-dir results \
    --output-filename scan_001.pcd
```

### 高密度扫描

```bash
python run_simulation.py --preset high_density
```

## 配置文件使用

### 查看所有预设配置

```python
from config import list_presets
list_presets()
```

### 创建自定义配置

```python
from config import create_custom_config
from lidar_simulation import LidarSimulator

# 创建自定义配置
config = create_custom_config(
    position=(5, 5, 10),
    direction=(-1, -1, -1),
    horizontal_fov=90,
    vertical_fov=30,
    horizontal_resolution=0.5,
    vertical_resolution=0.5,
    max_range=40
)

# 使用配置
lidar = LidarSimulator(**config)
```

## 性能优化

### 1. 调整分辨率

```python
# 低分辨率 - 快速测试
config = {
    "horizontal_resolution": 5.0,
    "vertical_resolution": 5.0
}

# 中等分辨率 - 平衡质量和速度
config = {
    "horizontal_resolution": 1.0,
    "vertical_resolution": 1.0
}

# 高分辨率 - 高质量但慢
config = {
    "horizontal_resolution": 0.1,
    "vertical_resolution": 0.1
}
```

### 2. 限制视场角

```python
# 全方位扫描 - 慢
config = {"horizontal_fov": 360.0}

# 半圆扫描 - 中等
config = {"horizontal_fov": 180.0}

# 窄角扫描 - 快
config = {"horizontal_fov": 90.0}
```

### 3. 调整探测距离

```python
# 短距离 - 快
config = {"max_range": 20.0}

# 中等距离 - 平衡
config = {"max_range": 50.0}

# 长距离 - 慢
config = {"max_range": 100.0}
```

## 输出文件格式

### PCD 文件

生成的 `.pcd` 文件可以用以下工具打开：

- **CloudCompare**: 免费的点云处理软件
- **PCL Viewer**: PCL 库自带的查看器
- **MeshLab**: 3D 网格处理软件
- **Open3D**: Python 中重新加载

```python
import open3d as o3d
pc = o3d.io.read_point_cloud("lidar_scan.pcd")
o3d.visualization.draw_geometries([pc])
```

## 故障排除

### 1. Open3D 安装问题

```bash
# 如果 pip 安装失败，尝试 conda
conda install -c open3d-admin open3d

# 或者使用预编译版本
pip install open3d --upgrade
```

### 2. GLB 文件加载失败

- 确保 GLB 文件有效（可以用 Blender 等软件验证）
- 检查文件路径是否正确
- 尝试转换为 OBJ 或 PLY 格式

### 3. 内存不足

- 降低分辨率参数
- 减小视场角
- 使用更简单的模型

### 4. 可视化问题

- 更新显卡驱动
- 检查 OpenGL 支持
- 尝试使用 `--no-visualization` 参数

### 5. 性能问题

```bash
# 使用快速测试配置
python run_simulation.py --preset quick_test

# 检查性能估算
python config.py
```

## 进阶使用

### 批量处理

```python
from config import LIDAR_PRESETS
from run_simulation import run_simulation_with_config

# 批量运行所有预设配置
for preset_name, config in LIDAR_PRESETS.items():
    print(f"运行配置: {preset_name}")
    run_simulation_with_config(
        config=config,
        output_filename=f"{preset_name}_scan.pcd",
        show_visualization=False
    )
```

### 自定义场景

```python
import open3d as o3d
from lidar_simulation import LidarSimulator

# 创建自定义场景
scene = o3d.geometry.TriangleMesh()
# ... 添加几何体 ...

# 运行仿真
lidar = LidarSimulator(position=(0,0,10), direction=(0,0,-1))
pc = lidar.simulate_lidar_scan(scene)
```

## 获取帮助

如果遇到问题：

1. 首先运行 `python test_system.py` 检查系统状态
2. 查看错误信息和日志
3. 检查本文档的故障排除部分
4. 确保所有依赖正确安装
