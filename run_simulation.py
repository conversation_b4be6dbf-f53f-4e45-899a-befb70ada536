#!/usr/bin/env python3
"""
激光雷达仿真运行脚本
提供命令行界面来运行不同的仿真配置
"""

import argparse
import os
import sys
from lidar_simulation import LidarSimulator
from config import get_preset_config, list_presets, validate_config, estimate_performance
import open3d as o3d


def create_output_directory(output_dir="output"):
    """创建输出目录"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    return output_dir


def load_model_or_create_scene(model_path=None):
    """加载模型或创建测试场景"""
    if model_path and os.path.exists(model_path):
        print(f"加载模型文件: {model_path}")
        mesh = o3d.io.read_triangle_mesh(model_path)
        if len(mesh.vertices) == 0:
            raise ValueError(f"模型文件加载失败或为空: {model_path}")
        if not mesh.has_vertex_normals():
            mesh.compute_vertex_normals()
        return mesh
    else:
        if model_path:
            print(f"警告: 模型文件不存在: {model_path}")
        print("创建默认测试场景...")
        return create_default_scene()


def create_default_scene():
    """创建默认测试场景"""
    # 创建地面
    ground = o3d.geometry.TriangleMesh.create_box(width=30, height=30, depth=0.2)
    ground.translate([-15, -15, -0.2])
    ground.paint_uniform_color([0.6, 0.6, 0.6])
    
    # 创建建筑物
    buildings = []
    building_configs = [
        {"size": (4, 4, 6), "pos": (5, 5, 0), "color": (0.8, 0.4, 0.4)},
        {"size": (3, 6, 4), "pos": (-8, 3, 0), "color": (0.4, 0.8, 0.4)},
        {"size": (5, 3, 8), "pos": (2, -10, 0), "color": (0.4, 0.4, 0.8)},
        {"size": (2, 2, 3), "pos": (-5, -5, 0), "color": (0.8, 0.8, 0.4)},
    ]
    
    for config in building_configs:
        building = o3d.geometry.TriangleMesh.create_box(*config["size"])
        building.translate(config["pos"])
        building.paint_uniform_color(config["color"])
        buildings.append(building)
    
    # 创建圆柱体（树木或柱子）
    cylinders = []
    cylinder_configs = [
        {"radius": 0.4, "height": 3, "pos": (-3, 8, 0), "color": (0.2, 0.8, 0.2)},
        {"radius": 0.3, "height": 2.5, "pos": (8, -3, 0), "color": (0.2, 0.8, 0.2)},
        {"radius": 0.5, "height": 4, "pos": (0, 0, 0), "color": (0.2, 0.8, 0.2)},
    ]
    
    for config in cylinder_configs:
        cylinder = o3d.geometry.TriangleMesh.create_cylinder(
            radius=config["radius"], height=config["height"]
        )
        cylinder.translate(config["pos"])
        cylinder.paint_uniform_color(config["color"])
        cylinders.append(cylinder)
    
    # 合并所有几何体
    scene_mesh = ground
    for building in buildings:
        scene_mesh += building
    for cylinder in cylinders:
        scene_mesh += cylinder
    
    scene_mesh.compute_vertex_normals()
    return scene_mesh


def run_simulation_with_config(config, model_path=None, output_dir="output", 
                              output_filename=None, show_visualization=True):
    """使用指定配置运行仿真"""
    
    # 验证配置
    if not validate_config(config):
        raise ValueError("配置验证失败")
    
    # 显示性能估算
    perf = estimate_performance(config)
    print(f"\n性能估算:")
    print(f"  射线数量: {perf['total_rays']:,}")
    print(f"  预计内存: {perf['estimated_memory_mb']:.1f} MB")
    print(f"  预计时间: {perf['estimated_time_seconds']:.1f} 秒")
    print(f"  性能等级: {perf['performance_level']}")
    
    # 如果射线数量过多，询问用户是否继续
    if perf['total_rays'] > 100000:
        try:
            response = input(f"\n射线数量较多 ({perf['total_rays']:,})，可能需要较长时间。是否继续？(y/n): ")
            if response.lower().strip() not in ['y', 'yes', '是']:
                print("仿真已取消")
                return None
        except KeyboardInterrupt:
            print("\n仿真已取消")
            return None
    
    # 创建激光雷达仿真器
    print(f"\n创建激光雷达仿真器...")
    lidar = LidarSimulator(**{k: v for k, v in config.items() if k != 'description'})
    
    # 加载模型或创建场景
    mesh = load_model_or_create_scene(model_path)
    
    # 执行仿真
    print(f"\n开始激光雷达仿真...")
    point_cloud = lidar.simulate_lidar_scan(mesh)
    
    # 保存结果
    output_dir = create_output_directory(output_dir)
    if output_filename is None:
        output_filename = "lidar_scan.pcd"
    
    output_path = os.path.join(output_dir, output_filename)
    lidar.save_point_cloud(point_cloud, output_path)
    
    # 可视化
    if show_visualization and len(point_cloud.points) > 0:
        print(f"\n启动可视化...")
        lidar.visualize(mesh, point_cloud)
    
    return point_cloud, mesh


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="激光雷达点云投影仿真")
    
    # 基本参数
    parser.add_argument("--preset", "-p", type=str, 
                       help="使用预设配置 (使用 --list-presets 查看可用配置)")
    parser.add_argument("--list-presets", action="store_true",
                       help="列出所有可用的预设配置")
    parser.add_argument("--model", "-m", type=str,
                       help="GLB/OBJ/PLY模型文件路径")
    parser.add_argument("--output-dir", "-o", type=str, default="output",
                       help="输出目录 (默认: output)")
    parser.add_argument("--output-filename", "-f", type=str,
                       help="输出PCD文件名 (默认: lidar_scan.pcd)")
    parser.add_argument("--no-visualization", action="store_true",
                       help="不显示可视化")
    
    # 自定义激光雷达参数
    parser.add_argument("--position", nargs=3, type=float, metavar=("X", "Y", "Z"),
                       help="激光雷达位置坐标")
    parser.add_argument("--direction", nargs=3, type=float, metavar=("X", "Y", "Z"),
                       help="激光雷达朝向方向")
    parser.add_argument("--horizontal-fov", type=float,
                       help="水平视场角 (度)")
    parser.add_argument("--vertical-fov", type=float,
                       help="垂直视场角 (度)")
    parser.add_argument("--horizontal-resolution", type=float,
                       help="水平角分辨率 (度)")
    parser.add_argument("--vertical-resolution", type=float,
                       help="垂直角分辨率 (度)")
    parser.add_argument("--max-range", type=float,
                       help="最大探测距离 (米)")
    
    args = parser.parse_args()
    
    # 列出预设配置
    if args.list_presets:
        list_presets()
        return
    
    # 确定使用的配置
    config = None
    
    if args.preset:
        # 使用预设配置
        try:
            config = get_preset_config(args.preset)
            print(f"使用预设配置: {args.preset}")
            print(f"描述: {config.get('description', '无描述')}")
        except ValueError as e:
            print(f"错误: {e}")
            return
    
    # 应用命令行参数覆盖
    if config is None:
        config = {
            "position": (0, 0, 5),
            "direction": (0, 0, -1),
            "horizontal_fov": 120.0,
            "vertical_fov": 30.0,
            "horizontal_resolution": 2.0,
            "vertical_resolution": 2.0,
            "max_range": 50.0,
            "description": "默认配置"
        }
        print("使用默认配置")
    
    # 应用命令行参数覆盖
    if args.position:
        config["position"] = tuple(args.position)
    if args.direction:
        config["direction"] = tuple(args.direction)
    if args.horizontal_fov:
        config["horizontal_fov"] = args.horizontal_fov
    if args.vertical_fov:
        config["vertical_fov"] = args.vertical_fov
    if args.horizontal_resolution:
        config["horizontal_resolution"] = args.horizontal_resolution
    if args.vertical_resolution:
        config["vertical_resolution"] = args.vertical_resolution
    if args.max_range:
        config["max_range"] = args.max_range
    
    # 运行仿真
    try:
        result = run_simulation_with_config(
            config=config,
            model_path=args.model,
            output_dir=args.output_dir,
            output_filename=args.output_filename,
            show_visualization=not args.no_visualization
        )
        
        if result:
            point_cloud, mesh = result
            print(f"\n仿真完成!")
            print(f"生成点云点数: {len(point_cloud.points):,}")
            print(f"输出目录: {args.output_dir}")
        
    except Exception as e:
        print(f"仿真失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
