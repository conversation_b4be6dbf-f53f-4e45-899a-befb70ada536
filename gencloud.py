import os
import json
import time
import numpy as np
import open3d as o3d
import open3d.t.geometry as o3dt
from typing import List

# ---------- 一次加载所有模型 ----------
def _build_scene(models: list) -> o3dt.RaycastingScene:
    scene = o3dt.RaycastingScene()
    for m in models:
        mesh = o3d.io.read_triangle_mesh(m["path"])
        mesh.scale(0.01, center=(0, 0, 0))
        mesh.compute_vertex_normals()
        T = np.eye(4)
        T[:3, 3] = m["center"]
        T[:3, :3] = o3d.geometry.get_rotation_matrix_from_xyz(np.radians(m["angle_deg"]))
        mesh.transform(T)
        scene.add_triangles(o3dt.TriangleMesh.from_legacy(mesh))
    return scene


def _sensor_to_matrix(sensor: dict) -> np.ndarray:
    """
    根据 JSON 中的 quat 或 rot_deg 字段，生成 3×3 旋转矩阵。
    quat 格式 [x,y,z,w]；rot_deg 格式 [rx,ry,rz]（度）。
    """
    if "quat" in sensor:
        q = sensor["quat"]          # [x,y,z,w]
        return o3d.geometry.get_rotation_matrix_from_quaternion([q[3], q[0], q[1], q[2]])
    else:
        return o3d.geometry.get_rotation_matrix_from_xyz(np.radians(sensor["rot_deg"]))

# ---------- 生成并保存帧 ----------
def lidar_frame_pcds(json_str: str,
                     out_dir: str = "frames",
                     timestamp_ms: int = None) -> List[o3d.geometry.PointCloud]:
    cfg = json.loads(json_str)
    os.makedirs(out_dir, exist_ok=True)

    scene = _build_scene(cfg["models"])

    rays_all, sensor_slices = [], []
    offset = 0
    for s in cfg["sensors"]:
        pos = np.asarray(s["pos"], dtype=np.float32)
        R = _sensor_to_matrix(s)  # 任意三维朝向

        h_fov = np.radians(s["h_fov_deg"])
        v_fov = np.radians(s["v_fov_deg"])
        h_res = np.radians(s["h_res_deg"])
        v_res = np.radians(s["v_res_deg"])

        h_angs = np.arange(-h_fov/2, h_fov/2 + 1e-6, h_res, dtype=np.float32)
        v_angs = np.arange(-v_fov/2, v_fov/2 + 1e-6, v_res, dtype=np.float32)
        hh, vv = np.meshgrid(h_angs, v_angs, indexing="xy")
        hh, vv = hh.ravel(), vv.ravel()

        # 激光局部坐标系 -> 世界坐标系
        dirs = np.stack([np.cos(hh) * np.cos(vv),
                         np.sin(hh) * np.cos(vv),
                         np.sin(vv)], axis=1)  # (N,3)
        dirs = dirs @ R.T  # 旋转到世界

        rays = np.hstack([np.tile(pos, (dirs.shape[0], 1)), dirs])
        rays_all.append(rays.astype(np.float32))
        sensor_slices.append((offset, offset + rays.shape[0]))
        offset += rays.shape[0]

    # 以下与之前完全一致：一次光线投射 → 切片 → 封装 PointCloud → 保存 PCD
    rays_cat = np.vstack(rays_all)
    ans = scene.cast_rays(o3d.core.Tensor(rays_cat))
    hit = ans["t_hit"].numpy()
    pts = rays_cat[:, :3] + rays_cat[:, 3:6] * hit[..., None]

    ts = str(int(time.time() * 1000) if timestamp_ms is None else timestamp_ms)
    pcds = []
    for idx, (start, end) in enumerate(sensor_slices):
        mask = hit[start:end] < np.inf
        pc = o3d.geometry.PointCloud()
        pc.points = o3d.utility.Vector3dVector(pts[start:end][mask])
        pcds.append(pc)
        o3d.io.write_point_cloud(os.path.join(out_dir, f"sensor{idx}_{ts}.pcd"), pc)

    return pcds
if __name__ == '__main__':
    with open("config.json", encoding="utf-8") as f:
     cfg_json = f.read()
    pcds = lidar_frame_pcds(cfg_json, out_dir="pcd_frames")
    print(f"传感器数：{len(pcds)}")
    for i, pc in enumerate(pcds):
      print(f"sensor[{i}] 点数：{len(pc.points)}")
      o3d.visualization.draw_geometries([pc])  # 直接可视化