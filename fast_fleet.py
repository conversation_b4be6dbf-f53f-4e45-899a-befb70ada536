import os, time, math, concurrent.futures, multiprocessing
import numpy as np
import open3d as o3d
from open3d.t.geometry import RaycastingScene

# ---------- 全局常量 ----------
N_CARS     = 20
H_FOV, V_FOV = 180.0, 45.0
H_RES, V_RES = 0.2, 0.2          # 精度可调
MAX_RANGE    = 30.0
OUT_DIR      = "fleet_output"

# ---------- 预生成方向 ----------
def _build_dirs():
    h = np.deg2rad(np.arange(-H_FOV/2, H_FOV/2+1e-6, H_RES))
    v = np.deg2rad(np.arange(-V_FOV/2, V_FOV/2+1e-6, V_RES))
    hh, vv = np.meshgrid(h, v)
    hh, vv = hh.ravel(), vv.ravel()
    dirs = np.stack([
        np.cos(vv) * np.sin(hh),
        np.sin(vv),
        -np.cos(vv) * np.cos(hh)
    ], -1).astype(np.float32)          # (N_dir, 3)
    return dirs

DIRS = _build_dirs()
N_DIR = len(DIRS)

# ---------- 工具 ----------
def load_merge_cars(car_paths):
    """把 20 辆车合并成一个 TriangleMesh"""
    mesh = o3d.geometry.TriangleMesh()
    for i, path in enumerate(car_paths):
        m = o3d.io.read_triangle_mesh(path)
        if not m.has_vertices():
            raise RuntimeError(f"{path} is empty")
        # 简单平铺成一排，间距 4m
        m.translate([i*4, 0, 0])
        mesh += m
    mesh.scale(0.01, center=(0,0,0))
    mesh.compute_vertex_normals()
    return mesh

def build_scene(mesh):
    scene = RaycastingScene()
    scene.add_triangles(o3d.t.geometry.TriangleMesh.from_legacy(mesh))
    return scene

# ---------- 一次扫描 ----------
def scan_frame(scene, lidar_poses):
    """
    lidar_poses: (N_CARS, 3) ndarray
    return list[PointCloud]
    """
    # 构造 rays: (N_CARS * N_DIR, 6)
    origins = np.repeat(lidar_poses, N_DIR, axis=0)          # (N*D,3)
    dirs_tiled = np.tile(DIRS, (N_CARS, 1))                  # (N*D,3)
    rays = np.hstack([origins, dirs_tiled]).astype(np.float32)

    ans = scene.cast_rays(rays)
    t_hit = ans['t_hit'].numpy()            # (N*D,)
    hit_mask = (t_hit > 0) & (t_hit < MAX_RANGE)

    # 计算点坐标
    pts = origins + dirs_tiled * t_hit[:, None]

    # 切片成 20 份
    clouds = []
    for i in range(N_CARS):
        off0, off1 = i * N_DIR, (i+1) * N_DIR
        mask = hit_mask[off0:off1]
        pc = o3d.geometry.PointCloud()
        pc.points = o3d.utility.Vector3dVector(pts[off0:off1][mask])
        clouds.append(pc)
    return clouds

# ---------- 主函数 ----------
def run_fleet(car_model_path, lidar_start, lidar_end):
    """
    car_model_path: 单个 glb 路径，会被复制 20 次
    lidar_start/end: (3,) ndarray, 20 台 LiDAR 线性插值在这两个位置之间
    """
    os.makedirs(OUT_DIR, exist_ok=True)

    # 1. 场景网格
    mesh = load_merge_cars([car_model_path]*N_CARS)
    scene = build_scene(mesh)

    # 2. 20 台 LiDAR 位置（单帧）
    lidar_poses = np.linspace(lidar_start, lidar_end, N_CARS)

    # 3. 单帧扫描
    t0 = time.perf_counter()
    clouds = scan_frame(scene, lidar_poses)
    dt = time.perf_counter() - t0
    print(f"单帧扫描耗时 {dt*1000:.1f} ms  "
          f"≈ {1/dt:.1f} FPS  (光线={N_CARS*N_DIR})")

    # 4. 并行保存
    def save(i, pc):
        if len(pc.points):
            path = f"{OUT_DIR}/car_{i:02d}.pcd"
            o3d.io.write_point_cloud(path, pc, print_progress=False)
        return len(pc.points)

    with concurrent.futures.ThreadPoolExecutor(N_CARS) as pool:
        futures = [pool.submit(save, i, c) for i, c in enumerate(clouds)]
        pts = [f.result() for f in concurrent.futures.as_completed(futures)]

    print(f"保存完成: {sum(pts)} 点 / {N_CARS} 份文件")

# ---------- CLI ----------
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("car_glb")
    parser.add_argument("--start", nargs=3, type=float, default=[0,0,8])
    parser.add_argument("--end",   nargs=3, type=float, default=[0,0,18])
    args = parser.parse_args()

    run_fleet(args.car_glb,
              np.array(args.start),
              np.array(args.end))