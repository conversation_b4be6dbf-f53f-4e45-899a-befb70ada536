#!/usr/bin/env python3
"""
激光雷达点云投影仿真演示脚本
展示系统的主要功能
"""

import os
import time
from lidar_simulation import LidarSimulator
from config import get_preset_config, list_presets
import open3d as o3d


def demo_1_basic_simulation():
    """演示1: 基本仿真功能"""
    print("\n" + "="*60)
    print("演示1: 基本激光雷达仿真")
    print("="*60)
    
    # 创建激光雷达
    lidar = LidarSimulator(
        position=(0, 0, 8),
        direction=(0, 0, -1),
        horizontal_fov=180.0,
        vertical_fov=45.0,
        horizontal_resolution=3.0,
        vertical_resolution=3.0,
        max_range=25.0
    )
    
    # 创建测试场景
    print("创建测试场景...")
    ground = o3d.geometry.TriangleMesh.create_box(width=20, height=20, depth=0.2)
    ground.translate([-10, -10, -0.2])
    
    building = o3d.geometry.TriangleMesh.create_box(width=4, height=4, depth=6)
    building.translate([3, 3, 0])
    
    cylinder = o3d.geometry.TriangleMesh.create_cylinder(radius=1, height=3)
    cylinder.translate([-5, -5, 0])
    
    scene = ground + building + cylinder
    scene.compute_vertex_normals()
    
    # 执行扫描
    print("执行激光雷达扫描...")
    point_cloud = lidar.simulate_lidar_scan(scene)
    
    # 保存结果
    output_file = "demo_1_basic.pcd"
    lidar.save_point_cloud(point_cloud, output_file)
    
    print(f"✓ 演示1完成 - 生成了 {len(point_cloud.points)} 个点")
    print(f"✓ 点云已保存到: {output_file}")
    
    return scene, point_cloud, lidar


def demo_2_preset_configurations():
    """演示2: 预设配置"""
    print("\n" + "="*60)
    print("演示2: 预设配置展示")
    print("="*60)
    
    # 显示可用预设
    print("可用的预设配置:")
    list_presets()
    
    # 使用几个不同的预设
    presets_to_test = ["quick_test", "automotive", "drone"]
    
    for preset_name in presets_to_test:
        print(f"\n测试预设: {preset_name}")
        config = get_preset_config(preset_name)
        
        # 创建激光雷达
        lidar = LidarSimulator(**{k: v for k, v in config.items() if k != 'description'})
        
        # 创建简单场景
        scene = o3d.geometry.TriangleMesh.create_box(width=5, height=5, depth=1)
        scene.translate([-2.5, -2.5, -1])
        scene.compute_vertex_normals()
        
        # 执行扫描
        point_cloud = lidar.simulate_lidar_scan(scene)
        
        # 保存结果
        output_file = f"demo_2_{preset_name}.pcd"
        lidar.save_point_cloud(point_cloud, output_file)
        
        print(f"  ✓ {preset_name}: {len(point_cloud.points)} 个点 -> {output_file}")


def demo_3_glb_model_scanning():
    """演示3: GLB模型扫描"""
    print("\n" + "="*60)
    print("演示3: GLB模型扫描")
    print("="*60)
    
    # 查找可用的GLB文件
    glb_files = [f for f in os.listdir('.') if f.endswith('.glb')]
    
    if not glb_files:
        print("未找到GLB文件，跳过此演示")
        return
    
    print(f"找到GLB文件: {glb_files}")
    
    for glb_file in glb_files[:2]:  # 最多测试2个文件
        print(f"\n扫描模型: {glb_file}")
        
        try:
            # 创建激光雷达
            lidar = LidarSimulator(
                position=(0, 0, 10),
                direction=(0, 0, -1),
                horizontal_fov=360.0,
                vertical_fov=60.0,
                horizontal_resolution=5.0,
                vertical_resolution=5.0,
                max_range=50.0
            )
            
            # 加载GLB模型
            mesh = lidar.load_glb_model(glb_file)
            
            # 执行扫描
            point_cloud = lidar.simulate_lidar_scan(mesh)
            
            # 保存结果
            output_file = f"demo_3_{os.path.splitext(glb_file)[0]}.pcd"
            lidar.save_point_cloud(point_cloud, output_file)
            
            print(f"  ✓ {glb_file}: {len(point_cloud.points)} 个点 -> {output_file}")
            
        except Exception as e:
            print(f"  ✗ {glb_file}: 扫描失败 - {e}")


def demo_4_different_angles():
    """演示4: 不同角度扫描"""
    print("\n" + "="*60)
    print("演示4: 不同角度和位置扫描")
    print("="*60)
    
    # 创建复杂一点的场景
    print("创建复杂场景...")
    scene_objects = []
    
    # 地面
    ground = o3d.geometry.TriangleMesh.create_box(width=30, height=30, depth=0.1)
    ground.translate([-15, -15, -0.1])
    scene_objects.append(ground)
    
    # 多个建筑物
    buildings = [
        {"size": (3, 3, 5), "pos": (5, 5, 0)},
        {"size": (4, 2, 3), "pos": (-8, 3, 0)},
        {"size": (2, 4, 4), "pos": (2, -10, 0)},
        {"size": (3, 3, 6), "pos": (-5, -5, 0)},
    ]
    
    for building in buildings:
        mesh = o3d.geometry.TriangleMesh.create_box(*building["size"])
        mesh.translate(building["pos"])
        scene_objects.append(mesh)
    
    # 合并场景
    scene = scene_objects[0]
    for obj in scene_objects[1:]:
        scene += obj
    scene.compute_vertex_normals()
    
    # 不同的扫描配置
    scan_configs = [
        {
            "name": "overhead",
            "position": (0, 0, 12),
            "direction": (0, 0, -1),
            "description": "俯视扫描"
        },
        {
            "name": "side_north",
            "position": (0, -20, 6),
            "direction": (0, 1, -0.3),
            "description": "北侧扫描"
        },
        {
            "name": "corner",
            "position": (15, 15, 8),
            "direction": (-1, -1, -0.5),
            "description": "角落扫描"
        },
        {
            "name": "low_angle",
            "position": (-12, 0, 3),
            "direction": (1, 0, 0),
            "description": "低角度侧扫"
        }
    ]
    
    for config in scan_configs:
        print(f"\n执行{config['description']}...")
        
        # 创建激光雷达
        lidar = LidarSimulator(
            position=config["position"],
            direction=config["direction"],
            horizontal_fov=120.0,
            vertical_fov=45.0,
            horizontal_resolution=3.0,
            vertical_resolution=3.0,
            max_range=30.0
        )
        
        # 执行扫描
        point_cloud = lidar.simulate_lidar_scan(scene)
        
        # 保存结果
        output_file = f"demo_4_{config['name']}.pcd"
        lidar.save_point_cloud(point_cloud, output_file)
        
        print(f"  ✓ {config['description']}: {len(point_cloud.points)} 个点 -> {output_file}")


def demo_5_visualization():
    """演示5: 可视化功能"""
    print("\n" + "="*60)
    print("演示5: 可视化功能")
    print("="*60)
    
    # 创建激光雷达
    lidar = LidarSimulator(
        position=(5, 5, 8),
        direction=(-1, -1, -1),
        horizontal_fov=90.0,
        vertical_fov=45.0,
        horizontal_resolution=2.0,
        vertical_resolution=2.0,
        max_range=20.0
    )
    
    # 创建有趣的场景
    print("创建可视化场景...")
    
    # 地面
    ground = o3d.geometry.TriangleMesh.create_box(width=20, height=20, depth=0.1)
    ground.translate([-10, -10, -0.1])
    ground.paint_uniform_color([0.5, 0.5, 0.5])
    
    # 彩色建筑物
    building1 = o3d.geometry.TriangleMesh.create_box(width=3, height=3, depth=4)
    building1.translate([0, 0, 0])
    building1.paint_uniform_color([0.8, 0.2, 0.2])  # 红色
    
    building2 = o3d.geometry.TriangleMesh.create_box(width=2, height=4, depth=3)
    building2.translate([-6, -2, 0])
    building2.paint_uniform_color([0.2, 0.8, 0.2])  # 绿色
    
    # 圆柱体
    cylinder = o3d.geometry.TriangleMesh.create_cylinder(radius=0.8, height=2.5)
    cylinder.translate([3, -6, 0])
    cylinder.paint_uniform_color([0.2, 0.2, 0.8])  # 蓝色
    
    # 球体
    sphere = o3d.geometry.TriangleMesh.create_sphere(radius=1.0)
    sphere.translate([-3, 4, 1])
    sphere.paint_uniform_color([0.8, 0.8, 0.2])  # 黄色
    
    scene = ground + building1 + building2 + cylinder + sphere
    scene.compute_vertex_normals()
    
    # 执行扫描
    print("执行扫描...")
    point_cloud = lidar.simulate_lidar_scan(scene)
    
    # 保存结果
    output_file = "demo_5_visualization.pcd"
    lidar.save_point_cloud(point_cloud, output_file)
    
    print(f"✓ 可视化演示完成 - 生成了 {len(point_cloud.points)} 个点")
    print(f"✓ 点云已保存到: {output_file}")
    
    # 询问是否显示可视化
    try:
        response = input("\n是否显示3D可视化？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("启动3D可视化...")
            lidar.visualize(scene, point_cloud)
    except KeyboardInterrupt:
        print("\n跳过可视化")
    
    return scene, point_cloud, lidar


def main():
    """主演示函数"""
    print("激光雷达点云投影仿真系统演示")
    print("=" * 60)
    print("本演示将展示系统的主要功能:")
    print("1. 基本仿真功能")
    print("2. 预设配置")
    print("3. GLB模型扫描")
    print("4. 不同角度扫描")
    print("5. 可视化功能")
    print("=" * 60)
    
    try:
        # 运行所有演示
        demo_1_basic_simulation()
        time.sleep(1)
        
        demo_2_preset_configurations()
        time.sleep(1)
        
        demo_3_glb_model_scanning()
        time.sleep(1)
        
        demo_4_different_angles()
        time.sleep(1)
        
        demo_5_visualization()
        
        print("\n" + "="*60)
        print("🎉 所有演示完成！")
        print("="*60)
        print("\n生成的文件:")
        pcd_files = [f for f in os.listdir('.') if f.startswith('demo_') and f.endswith('.pcd')]
        for pcd_file in pcd_files:
            print(f"  - {pcd_file}")
        
        print(f"\n总共生成了 {len(pcd_files)} 个点云文件")
        print("\n您可以使用以下工具查看点云文件:")
        print("- CloudCompare (推荐)")
        print("- PCL Viewer")
        print("- MeshLab")
        print("- 或者使用 Open3D 在 Python 中加载")
        
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
