"""
系统测试脚本
用于验证激光雷达仿真系统是否正常工作
"""

import numpy as np
import open3d as o3d
import sys
import traceback
from lidar_simulation import LidarSimulator


def test_dependencies():
    """测试依赖库是否正确安装"""
    print("测试依赖库...")
    
    try:
        import open3d as o3d
        print(f"✓ Open3D 版本: {o3d.__version__}")
    except ImportError:
        print("✗ Open3D 未安装")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy 版本: {np.__version__}")
    except ImportError:
        print("✗ NumPy 未安装")
        return False
    
    return True


def test_lidar_creation():
    """测试激光雷达创建"""
    print("\n测试激光雷达创建...")
    
    try:
        lidar = LidarSimulator(
            position=(0, 0, 5),
            direction=(0, 0, -1),
            horizontal_fov=90.0,
            vertical_fov=30.0,
            horizontal_resolution=5.0,
            vertical_resolution=5.0,
            max_range=20.0
        )
        print("✓ 激光雷达创建成功")
        return lidar
    except Exception as e:
        print(f"✗ 激光雷达创建失败: {e}")
        return None


def test_scene_creation():
    """测试场景创建"""
    print("\n测试场景创建...")
    
    try:
        # 创建简单的测试场景
        box = o3d.geometry.TriangleMesh.create_box(width=5, height=5, depth=1)
        box.translate([-2.5, -2.5, -1])
        box.compute_vertex_normals()
        
        print(f"✓ 测试场景创建成功 - 顶点数: {len(box.vertices)}, 面数: {len(box.triangles)}")
        return box
    except Exception as e:
        print(f"✗ 场景创建失败: {e}")
        return None


def test_ray_generation(lidar):
    """测试射线生成"""
    print("\n测试射线生成...")
    
    try:
        rays_origin, rays_direction = lidar.generate_rays()
        print(f"✓ 射线生成成功 - 射线数量: {len(rays_origin)}")
        
        # 验证射线数据
        if len(rays_origin) == len(rays_direction):
            print("✓ 射线起点和方向数量匹配")
        else:
            print("✗ 射线起点和方向数量不匹配")
            return False
        
        # 验证射线方向是单位向量
        norms = np.linalg.norm(rays_direction, axis=1)
        if np.allclose(norms, 1.0, atol=1e-6):
            print("✓ 射线方向为单位向量")
        else:
            print("✗ 射线方向不是单位向量")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 射线生成失败: {e}")
        traceback.print_exc()
        return False


def test_lidar_scan(lidar, mesh):
    """测试激光雷达扫描"""
    print("\n测试激光雷达扫描...")
    
    try:
        point_cloud = lidar.simulate_lidar_scan(mesh)
        
        if len(point_cloud.points) > 0:
            print(f"✓ 激光雷达扫描成功 - 点云点数: {len(point_cloud.points)}")
            return point_cloud
        else:
            print("⚠ 激光雷达扫描完成但未检测到点")
            return point_cloud
    except Exception as e:
        print(f"✗ 激光雷达扫描失败: {e}")
        traceback.print_exc()
        return None


def test_point_cloud_save(lidar, point_cloud):
    """测试点云保存"""
    print("\n测试点云保存...")
    
    try:
        filename = "test_scan.pcd"
        lidar.save_point_cloud(point_cloud, filename)
        
        # 验证文件是否创建
        import os
        if os.path.exists(filename):
            print(f"✓ 点云保存成功: {filename}")
            
            # 尝试重新加载验证
            loaded_pc = o3d.io.read_point_cloud(filename)
            if len(loaded_pc.points) == len(point_cloud.points):
                print("✓ 点云文件验证成功")
                return True
            else:
                print("✗ 点云文件验证失败")
                return False
        else:
            print("✗ 点云文件未创建")
            return False
    except Exception as e:
        print(f"✗ 点云保存失败: {e}")
        return False


def test_visualization_setup(lidar, mesh, point_cloud):
    """测试可视化设置（不实际显示）"""
    print("\n测试可视化设置...")
    
    try:
        # 创建可视化对象但不显示
        vis_objects = []
        
        # 添加原始模型
        import copy
        mesh_copy = copy.deepcopy(mesh)
        mesh_copy.paint_uniform_color([0.7, 0.7, 0.7])
        vis_objects.append(mesh_copy)
        
        # 添加点云
        if len(point_cloud.points) > 0:
            vis_objects.append(point_cloud)
        
        # 添加激光雷达位置标记
        lidar_marker = o3d.geometry.TriangleMesh.create_sphere(radius=0.2)
        lidar_marker.translate(lidar.position)
        lidar_marker.paint_uniform_color([0, 1, 0])
        vis_objects.append(lidar_marker)
        
        print(f"✓ 可视化对象创建成功 - 对象数量: {len(vis_objects)}")
        return True
    except Exception as e:
        print(f"✗ 可视化设置失败: {e}")
        return False


def run_full_test():
    """运行完整测试"""
    print("=" * 60)
    print("激光雷达仿真系统测试")
    print("=" * 60)
    
    # 测试依赖
    if not test_dependencies():
        print("\n❌ 依赖测试失败，请安装必要的库")
        return False
    
    # 测试激光雷达创建
    lidar = test_lidar_creation()
    if lidar is None:
        print("\n❌ 激光雷达创建测试失败")
        return False
    
    # 测试场景创建
    mesh = test_scene_creation()
    if mesh is None:
        print("\n❌ 场景创建测试失败")
        return False
    
    # 测试射线生成
    if not test_ray_generation(lidar):
        print("\n❌ 射线生成测试失败")
        return False
    
    # 测试激光雷达扫描
    point_cloud = test_lidar_scan(lidar, mesh)
    if point_cloud is None:
        print("\n❌ 激光雷达扫描测试失败")
        return False
    
    # 测试点云保存
    if not test_point_cloud_save(lidar, point_cloud):
        print("\n❌ 点云保存测试失败")
        return False
    
    # 测试可视化设置
    if not test_visualization_setup(lidar, mesh, point_cloud):
        print("\n❌ 可视化设置测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！系统运行正常")
    print("=" * 60)
    
    print("\n下一步:")
    print("1. 运行 'python lidar_simulation.py' 查看基本示例")
    print("2. 运行 'python example_usage.py' 查看多种扫描示例")
    print("3. 将您的GLB文件放入目录并修改代码中的路径")
    
    return True


def quick_demo():
    """快速演示"""
    print("\n" + "=" * 40)
    print("快速演示")
    print("=" * 40)
    
    try:
        # 创建简单配置的激光雷达
        lidar = LidarSimulator(
            position=(0, 0, 3),
            direction=(0, 0, -1),
            horizontal_fov=60.0,
            vertical_fov=20.0,
            horizontal_resolution=10.0,  # 低分辨率用于快速测试
            vertical_resolution=10.0,
            max_range=10.0
        )
        
        # 创建简单场景
        mesh = o3d.geometry.TriangleMesh.create_box(width=2, height=2, depth=0.1)
        mesh.translate([-1, -1, -0.1])
        mesh.compute_vertex_normals()
        
        # 执行扫描
        point_cloud = lidar.simulate_lidar_scan(mesh)
        
        # 保存结果
        lidar.save_point_cloud(point_cloud, "quick_demo.pcd")
        
        print(f"✓ 快速演示完成 - 生成了 {len(point_cloud.points)} 个点")
        
        # 询问是否显示可视化
        try:
            response = input("\n是否显示可视化？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                lidar.visualize(mesh, point_cloud)
        except KeyboardInterrupt:
            print("\n跳过可视化")
        
    except Exception as e:
        print(f"快速演示失败: {e}")


if __name__ == "__main__":
    # 运行完整测试
    success = run_full_test()
    
    if success:
        # 运行快速演示
        quick_demo()
    else:
        print("\n请解决上述问题后重新运行测试")
        sys.exit(1)
