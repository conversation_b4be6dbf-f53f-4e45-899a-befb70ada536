#!/usr/bin/env python3
"""
专门用于扫描car.glb模型的脚本
自动分析模型并优化扫描参数
"""

import numpy as np
import open3d as o3d
from lidar_simulation import LidarSimulator
import os


def analyze_model(glb_path):
    """分析GLB模型的基本信息"""
    print(f"分析模型: {glb_path}")
    
    if not os.path.exists(glb_path):
        raise FileNotFoundError(f"模型文件不存在: {glb_path}")
    
    # 加载模型
    mesh = o3d.io.read_triangle_mesh(glb_path)
    
    if len(mesh.vertices) == 0:
        raise ValueError("模型加载失败或为空")
    
    # 确保有法向量
    if not mesh.has_vertex_normals():
        mesh.compute_vertex_normals()
    
    # 计算边界框
    bbox = mesh.get_axis_aligned_bounding_box()
    min_bound = bbox.min_bound
    max_bound = bbox.max_bound
    center = bbox.get_center()
    size = max_bound - min_bound
    
    print(f"模型信息:")
    print(f"  顶点数: {len(mesh.vertices):,}")
    print(f"  三角面数: {len(mesh.triangles):,}")
    print(f"  边界框最小点: [{min_bound[0]:.2f}, {min_bound[1]:.2f}, {min_bound[2]:.2f}]")
    print(f"  边界框最大点: [{max_bound[0]:.2f}, {max_bound[1]:.2f}, {max_bound[2]:.2f}]")
    print(f"  中心点: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]")
    print(f"  尺寸: [{size[0]:.2f}, {size[1]:.2f}, {size[2]:.2f}]")
    print(f"  最大尺寸: {max(size):.2f}")
    
    return mesh, bbox, center, size


def create_optimized_scan_configs(center, size, bbox):
    """根据模型特征创建优化的扫描配置"""
    max_dimension = max(size)
    min_bound = bbox.min_bound
    max_bound = bbox.max_bound
    
    configs = [
        {
            "name": "车顶俯视",
            "description": "从车顶上方向下扫描，适合获取车辆轮廓",
            "position": (center[0], center[1], max_bound[2] + max_dimension * 0.2),
            "direction": (0, 0, -1),
            "horizontal_fov": 360.0,
            "vertical_fov": 90.0,
            "horizontal_resolution": 0.5,
            "vertical_resolution": 1.0,
            "max_range": max_dimension * 1.5
        },
        {
            "name": "车头正面",
            "description": "从车头正面扫描",
            "position": (center[0], min_bound[1] - max_dimension * 0.3, center[2] + size[2] * 0.2),
            "direction": (0, 1, -0.1),
            "horizontal_fov": 120.0,
            "vertical_fov": 60.0,
            "horizontal_resolution": 0.5,
            "vertical_resolution": 0.5,
            "max_range": max_dimension * 1.2
        },
        {
            "name": "车侧45度",
            "description": "从车侧45度角扫描",
            "position": (min_bound[0] - max_dimension * 0.3, center[1] - max_dimension * 0.2, center[2] + size[2] * 0.3),
            "direction": (1, 0.5, -0.3),
            "horizontal_fov": 90.0,
            "vertical_fov": 60.0,
            "horizontal_resolution": 0.5,
            "vertical_resolution": 0.5,
            "max_range": max_dimension * 1.2
        },
        {
            "name": "全景扫描",
            "description": "远距离全景扫描",
            "position": (center[0] + max_dimension * 0.8, center[1] + max_dimension * 0.8, max_bound[2] + max_dimension * 0.4),
            "direction": (-1, -1, -0.8),
            "horizontal_fov": 120.0,
            "vertical_fov": 90.0,
            "horizontal_resolution": 1.0,
            "vertical_resolution": 1.0,
            "max_range": max_dimension * 2.0
        },
        {
            "name": "高密度细节",
            "description": "近距离高密度扫描",
            "position": (center[0] - max_dimension * 0.4, center[1], center[2] + size[2] * 0.1),
            "direction": (1, 0, 0),
            "horizontal_fov": 60.0,
            "vertical_fov": 45.0,
            "horizontal_resolution": 0.2,
            "vertical_resolution": 0.2,
            "max_range": max_dimension * 0.8
        }
    ]
    
    return configs


def scan_with_config(mesh, config, save_prefix="car"):
    """使用指定配置执行扫描"""
    print(f"\n{'='*60}")
    print(f"扫描配置: {config['name']}")
    print(f"描述: {config['description']}")
    print(f"{'='*60}")
    
    # 创建激光雷达
    lidar = LidarSimulator(
        position=config["position"],
        direction=config["direction"],
        horizontal_fov=config["horizontal_fov"],
        vertical_fov=config["vertical_fov"],
        horizontal_resolution=config["horizontal_resolution"],
        vertical_resolution=config["vertical_resolution"],
        max_range=config["max_range"]
    )
    
    # 执行扫描
    point_cloud = lidar.simulate_lidar_scan(mesh)
    
    if len(point_cloud.points) > 0:
        # 保存点云
        filename = f"{save_prefix}_{config['name'].replace(' ', '_')}.pcd"
        lidar.save_point_cloud(point_cloud, filename)
        
        print(f"✓ 扫描成功!")
        print(f"  生成点数: {len(point_cloud.points):,}")
        print(f"  保存文件: {filename}")
        
        return point_cloud, lidar, True
    else:
        print(f"✗ 扫描失败: 未检测到任何点")
        return None, lidar, False


def main():
    """主函数"""
    print("Car.glb 模型激光雷达扫描工具")
    print("="*60)
    
    # 模型路径
    glb_path = "C:/pyproject/trun_cloud/car.glb"
    
    try:
        # 分析模型
        mesh, bbox, center, size = analyze_model(glb_path)
        
        # 创建扫描配置
        configs = create_optimized_scan_configs(center, size, bbox)
        
        print(f"\n准备执行 {len(configs)} 种扫描配置...")
        
        successful_scans = []
        
        # 执行所有扫描配置
        for i, config in enumerate(configs, 1):
            print(f"\n[{i}/{len(configs)}] ", end="")
            
            point_cloud, lidar, success = scan_with_config(mesh, config)
            
            if success:
                successful_scans.append({
                    'config': config,
                    'point_cloud': point_cloud,
                    'lidar': lidar
                })
        
        # 总结结果
        print(f"\n{'='*60}")
        print(f"扫描完成总结")
        print(f"{'='*60}")
        print(f"成功扫描: {len(successful_scans)}/{len(configs)}")
        
        if successful_scans:
            print(f"\n成功的扫描配置:")
            for i, scan in enumerate(successful_scans, 1):
                config = scan['config']
                points = len(scan['point_cloud'].points)
                print(f"  {i}. {config['name']}: {points:,} 点")
            
            # 选择最佳扫描进行可视化
            best_scan = max(successful_scans, key=lambda x: len(x['point_cloud'].points))
            best_config = best_scan['config']
            best_points = len(best_scan['point_cloud'].points)
            
            print(f"\n最佳扫描: {best_config['name']} ({best_points:,} 点)")
            
            # 询问是否可视化
            try:
                response = input(f"\n是否可视化最佳扫描结果？(y/n): ").lower().strip()
                if response in ['y', 'yes', '是']:
                    print("启动可视化...")
                    best_scan['lidar'].visualize(mesh, best_scan['point_cloud'])
            except KeyboardInterrupt:
                print("\n跳过可视化")
            
            # 询问是否可视化所有结果
            try:
                response = input(f"\n是否依次可视化所有扫描结果？(y/n): ").lower().strip()
                if response in ['y', 'yes', '是']:
                    for scan in successful_scans:
                        config = scan['config']
                        print(f"\n可视化: {config['name']}")
                        scan['lidar'].visualize(mesh, scan['point_cloud'])
            except KeyboardInterrupt:
                print("\n跳过批量可视化")
        
        else:
            print("\n没有成功的扫描，可能需要调整参数")
            print("建议:")
            print("1. 检查模型是否正确加载")
            print("2. 调整激光雷达位置和方向")
            print("3. 增加扫描范围")
    
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
