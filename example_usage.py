"""
激光雷达仿真使用示例
"""

import numpy as np
import open3d as o3d
from lidar_simulation import LidarSimulator


def create_test_scene():
    """创建测试场景"""
    print("创建测试场景...")
    
    # 创建地面
    ground = o3d.geometry.TriangleMesh.create_box(width=50, height=50, depth=0.1)
    ground.translate([-25, -25, -0.1])
    ground.paint_uniform_color([0.5, 0.5, 0.5])
    
    # 创建几个建筑物
    building1 = o3d.geometry.TriangleMesh.create_box(width=5, height=5, depth=8)
    building1.translate([10, 10, 0])
    building1.paint_uniform_color([0.8, 0.4, 0.4])
    
    building2 = o3d.geometry.TriangleMesh.create_box(width=3, height=8, depth=6)
    building2.translate([-15, 5, 0])
    building2.paint_uniform_color([0.4, 0.8, 0.4])
    
    building3 = o3d.geometry.TriangleMesh.create_box(width=6, height=4, depth=10)
    building3.translate([5, -20, 0])
    building3.paint_uniform_color([0.4, 0.4, 0.8])
    
    # 创建一些圆柱体（树木或柱子）
    tree1 = o3d.geometry.TriangleMesh.create_cylinder(radius=0.5, height=4)
    tree1.translate([-5, -5, 0])
    tree1.paint_uniform_color([0.2, 0.8, 0.2])
    
    tree2 = o3d.geometry.TriangleMesh.create_cylinder(radius=0.3, height=3)
    tree2.translate([8, -8, 0])
    tree2.paint_uniform_color([0.2, 0.8, 0.2])
    
    # 合并所有几何体
    scene_mesh = ground + building1 + building2 + building3 + tree1 + tree2
    scene_mesh.compute_vertex_normals()
    
    return scene_mesh


def example_1_overhead_scan():
    """示例1: 俯视扫描"""
    print("\n=== 示例1: 俯视扫描 ===")
    
    # 创建激光雷达 - 从上方向下扫描
    lidar = LidarSimulator(
        position=(0, 0, 15),          # 15米高度
        direction=(0, 0, -1),         # 向下
        horizontal_fov=360.0,         # 全方位扫描
        vertical_fov=60.0,            # 垂直60度
        horizontal_resolution=2.0,    # 2度分辨率
        vertical_resolution=2.0,      # 2度分辨率
        max_range=30.0               # 最大30米
    )
    
    # 创建或加载场景
    mesh = create_test_scene()
    
    # 执行扫描
    point_cloud = lidar.simulate_lidar_scan(mesh)
    
    # 保存结果
    lidar.save_point_cloud(point_cloud, "overhead_scan.pcd")
    
    # 可视化
    lidar.visualize(mesh, point_cloud)


def example_2_side_scan():
    """示例2: 侧面扫描"""
    print("\n=== 示例2: 侧面扫描 ===")
    
    # 创建激光雷达 - 从侧面扫描
    lidar = LidarSimulator(
        position=(-30, 0, 5),         # 侧面位置，5米高度
        direction=(1, 0, 0),          # 向前
        horizontal_fov=120.0,         # 120度水平视角
        vertical_fov=45.0,            # 45度垂直视角
        horizontal_resolution=1.0,    # 1度分辨率
        vertical_resolution=1.0,      # 1度分辨率
        max_range=50.0               # 最大50米
    )
    
    # 创建场景
    mesh = create_test_scene()
    
    # 执行扫描
    point_cloud = lidar.simulate_lidar_scan(mesh)
    
    # 保存结果
    lidar.save_point_cloud(point_cloud, "side_scan.pcd")
    
    # 可视化
    lidar.visualize(mesh, point_cloud)


def example_3_angled_scan():
    """示例3: 倾斜扫描"""
    print("\n=== 示例3: 倾斜扫描 ===")
    
    # 创建激光雷达 - 倾斜角度扫描
    lidar = LidarSimulator(
        position=(20, 20, 10),        # 角落位置，10米高度
        direction=(-1, -1, -0.5),     # 向场景中心倾斜
        horizontal_fov=90.0,          # 90度水平视角
        vertical_fov=60.0,            # 60度垂直视角
        horizontal_resolution=1.5,    # 1.5度分辨率
        vertical_resolution=1.5,      # 1.5度分辨率
        max_range=40.0               # 最大40米
    )
    
    # 创建场景
    mesh = create_test_scene()
    
    # 执行扫描
    point_cloud = lidar.simulate_lidar_scan(mesh)
    
    # 保存结果
    lidar.save_point_cloud(point_cloud, "angled_scan.pcd")
    
    # 可视化
    lidar.visualize(mesh, point_cloud)


def example_4_high_density_scan():
    """示例4: 高密度扫描"""
    print("\n=== 示例4: 高密度扫描 ===")
    
    # 创建激光雷达 - 高密度扫描
    lidar = LidarSimulator(
        position=(0, 0, 8),           # 8米高度
        direction=(0, 0, -1),         # 向下
        horizontal_fov=180.0,         # 180度水平视角
        vertical_fov=30.0,            # 30度垂直视角
        horizontal_resolution=0.5,    # 0.5度高分辨率
        vertical_resolution=0.5,      # 0.5度高分辨率
        max_range=25.0               # 最大25米
    )
    
    # 创建场景
    mesh = create_test_scene()
    
    # 执行扫描
    point_cloud = lidar.simulate_lidar_scan(mesh)
    
    # 保存结果
    lidar.save_point_cloud(point_cloud, "high_density_scan.pcd")
    
    # 可视化
    lidar.visualize(mesh, point_cloud)


def load_and_scan_glb(scale=1.0, scale_config=None):
    """加载GLB文件并扫描
    
    Args:
        scale (float): 模型统一缩放比例，默认为1.0（原始大小）
        scale_config (dict, optional): 详细缩放配置，格式为：
            {
                'scale_x': 0.01,  # X轴缩放
                'scale_y': 0.01,  # Y轴缩放  
                'scale_z': 0.01,  # Z轴缩放
                'center': (0, 0, 0)  # 缩放中心点
            }
            如果提供此参数，将忽略scale参数
    """
    print("\n=== GLB文件扫描 ===")

    # 请将您的GLB文件路径替换为实际路径
    glb_path = "C:/pyproject/trun_cloud/car.glb"  # 替换为您的GLB文件路径

    try:
        # 首先加载模型并分析其边界
        print("分析模型...")
        import open3d as o3d
        mesh = o3d.io.read_triangle_mesh(glb_path)

        if len(mesh.vertices) == 0:
            print("模型加载失败或为空")
            return

        # 应用缩放变换
        if scale_config is not None:
            # 使用详细缩放配置
            scale_x = scale_config.get('scale_x', 0.01)
            scale_y = scale_config.get('scale_y', 0.01)
            scale_z = scale_config.get('scale_z', 0.01)
            center = scale_config.get('center', (0, 0, 0))
            
            print(f"应用详细缩放配置:")
            print(f"  X轴缩放: {scale_x}")
            print(f"  Y轴缩放: {scale_y}")
            print(f"  Z轴缩放: {scale_z}")
            print(f"  缩放中心: {center}")
            
            # 创建缩放变换矩阵
            import numpy as np
            scale_matrix = np.array([
                [scale_x, 0, 0, 0],
                [0, scale_y, 0, 0],
                [0, 0, scale_z, 0],
                [0, 0, 0, 1]
            ])
            
            # 应用缩放变换
            mesh.transform(scale_matrix)
            
        elif scale != 1.0:
            # 使用统一缩放
            print(f"应用统一缩放: {scale}")
            mesh.scale(scale, center=(0, 0, 0))

        # 计算模型边界
        bbox = mesh.get_axis_aligned_bounding_box()
        min_bound = bbox.min_bound
        max_bound = bbox.max_bound
        center = bbox.get_center()
        size = max_bound - min_bound

        print(f"模型边界框 (缩放后):")
        print(f"  最小点: {min_bound}")
        print(f"  最大点: {max_bound}")
        print(f"  中心点: {center}")
        print(f"  尺寸: {size}")
        print(f"  最大尺寸: {max(size):.2f}")

        # 确保模型有法向量
        if not mesh.has_vertex_normals():
            mesh.compute_vertex_normals()

        # 根据模型大小调整激光雷达位置
        max_dimension = max(size)
        lidar_height = max(max_bound[2] + max_dimension * 0.5, 5.0)  # 在模型上方
        scan_range = max_dimension * 2.0  # 扫描范围

        print(f"调整激光雷达参数:")
        print(f"  位置高度: {lidar_height:.2f}")
        print(f"  扫描范围: {scan_range:.2f}")

        # 创建激光雷达 - 多种配置尝试
        configs = [
            # {
            #     "name": "俯视扫描",
            #     "position": (center[0], center[1], max_bound[2] + max_dimension * 0.3),
            #     "direction": (0, 0, -1),
            #     "horizontal_fov": 360.0,
            #     "vertical_fov": 120.0,
            #     "resolution": 0.1
            # },
            # {
            #     "name": "侧面扫描",
            #     "position": (center[0] - max_dimension * 0.8, center[1], center[2]),
            #     "direction": (1, 0, 0),
            #     "horizontal_fov": 120.0,
            #     "vertical_fov": 90.0,
            #     "resolution": 1.0
            # },
            # {
            #     "name": "倾斜扫描",
            #     "position": (center[0] + max_dimension * 0.6, center[1] + max_dimension * 0.6, max_bound[2] + max_dimension * 0.2),
            #     "direction": (-1, -1, -0.8),
            #     "horizontal_fov": 90.0,
            #     "vertical_fov": 90.0,
            #     "resolution": 1.0
            # },
            {
                "name": "近距离扫描",
                "position": (center[0], center[1] - max_dimension * 0.4, center[2] + size[2] * 0.3),
                "direction": (0, 1, -0.2),
                "horizontal_fov": 180.0,
                "vertical_fov": 60.0,
                "resolution": 0.5
            }
        ]

        for i, config in enumerate(configs):
            print(f"\n尝试配置 {i+1}: {config['name']}")

            # 创建激光雷达
            lidar = LidarSimulator(
                position=config["position"],
                direction=config["direction"],
                horizontal_fov=config["horizontal_fov"],
                vertical_fov=config["vertical_fov"],
                horizontal_resolution=config["resolution"],
                vertical_resolution=config["resolution"],
                max_range=scan_range
            )

            # 执行扫描
            point_cloud = lidar.simulate_lidar_scan(mesh)

            if len(point_cloud.points) > 0:
                print(f"✓ 成功! 生成了 {len(point_cloud.points)} 个点")

                # 保存结果
                filename = f"car_scan_{config['name'].replace(' ', '_')}.pcd"
                lidar.save_point_cloud(point_cloud, filename)

                # 可视化
                print("启动可视化...")
                lidar.visualize(mesh, point_cloud)
                break
            else:
                print(f"✗ 未检测到点，尝试下一个配置...")

        else:
            print("所有配置都未能检测到点，可能需要手动调整参数")
        
    except FileNotFoundError:
        print(f"GLB文件未找到: {glb_path}")
        print("请将GLB文件放在当前目录或修改路径")
    except Exception as e:
        print(f"处理GLB文件时出错: {e}")


def real_time_moving_lidar(glb_path, start_position=(0, 0, 8), duration=10.0, speed=5.0, fps=10):
    """实时移动激光雷达仿真
    
    Args:
        glb_path (str): GLB文件路径
        start_position (tuple): 起始位置 (x, y, z)
        duration (float): 持续时间（秒）
        speed (float): 移动速度（单位/秒）
        fps (int): 帧率（每秒帧数）
    """
    print(f"\n=== 实时移动激光雷达仿真 ===")
    print(f"起始位置: {start_position}")
    print(f"持续时间: {duration}秒")
    print(f"移动速度: {speed} 单位/秒")
    print(f"帧率: {fps} FPS")
    
    try:
        # 加载GLB模型
        print("加载GLB模型...")
        import open3d as o3d
        mesh = o3d.io.read_triangle_mesh(glb_path)
        mesh.scale(0.01, center=(0, 0, 0))
        if len(mesh.vertices) == 0:
            print("模型加载失败或为空")
            return
        
        # 确保模型有法向量
        if not mesh.has_vertex_normals():
            mesh.compute_vertex_normals()
        
        # 计算总帧数
        total_frames = int(duration * fps)
        frame_interval = 1.0 / fps
        
        print(f"总帧数: {total_frames}")
        print(f"帧间隔: {frame_interval:.3f}秒")
        
        # 存储所有点云和位置信息
        all_point_clouds = []
        lidar_positions = []
        
        # 创建输出目录
        import os
        output_dir = "moving_lidar_output"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n开始实时仿真...")
        
        for frame in range(total_frames):
            # 计算当前时间
            current_time = frame * frame_interval
            
            # 计算当前位置（沿Y轴移动）
            # current_y = start_position[1] + speed * current_time
            # current_position = (start_position[0], current_y, start_position[2])

            current_z = start_position[2] + speed * current_time
            current_position = (start_position[0], start_position[1], current_z)

            print(f"帧 {frame+1}/{total_frames} - 时间: {current_time:.2f}s - 位置: {current_position}")
            
            # 创建激光雷达
            lidar = LidarSimulator(
                position=current_position,
                direction=(0, 0, -1),  # 向下扫描
                horizontal_fov=180.0,   # 180度水平视角
                vertical_fov=45.0,      # 45度垂直视角
                horizontal_resolution=0.1,
                vertical_resolution=0.1,
                max_range=30.0
            )
            
            # 执行扫描
            point_cloud = lidar.simulate_lidar_scan(mesh)
            
            # 保存点云
            if len(point_cloud.points) > 0:
                filename = os.path.join(output_dir, f"moving_lidar_frame_{frame:04d}.pcd")
                lidar.save_point_cloud(point_cloud, filename)
                all_point_clouds.append(point_cloud)
                lidar_positions.append(current_position)
                print(f"  ✓ 生成 {len(point_cloud.points)} 个点，已保存到 {filename}")
            else:
                print(f"  ✗ 未检测到点")
        
        # 可视化所有结果
        print(f"\n启动可视化...")
        visualize_moving_lidar_results(mesh, all_point_clouds, lidar_positions, start_position, speed, duration)
        
        print(f"\n✓ 实时移动激光雷达仿真完成!")
        print(f"✓ 共生成 {len(all_point_clouds)} 帧点云")
        print(f"✓ 所有文件保存在: {output_dir}/")
        
    except FileNotFoundError:
        print(f"GLB文件未找到: {glb_path}")
    except Exception as e:
        print(f"仿真过程中出错: {e}")


import os
import time
import numpy as np
import open3d as o3d
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

# 如果 open3d >= 0.16，RaycastingScene 在 t.geometry
try:
    from open3d.t.geometry import RaycastingScene
except ImportError:
    from open3d.core import RaycastingScene   # 兼容 0.15

# ---------- LiDAR 模拟（批量光线版） ----------
class FastLidar:
    def __init__(self,
                 h_fov=180.0, v_fov=45.0,
                 h_res=0.1, v_res=0.1,
                 max_range=30.0):
        self.h_fov, self.v_fov = h_fov, v_fov
        self.h_res, self.v_res = h_res, v_res
        self.max_range = max_range

        # 预生成方向向量（N x 3）
        h_angles = np.deg2rad(np.arange(-h_fov / 2, h_fov / 2 + 1e-6, h_res))
        v_angles = np.deg2rad(np.arange(-v_fov / 2, v_fov / 2 + 1e-6, v_res))
        hh, vv = np.meshgrid(h_angles, v_angles)
        hh, vv = hh.ravel(), vv.ravel()

        dirs = np.stack([
            np.cos(vv) * np.sin(hh),
            np.sin(vv),
            -np.cos(vv) * np.cos(hh)
        ], axis=-1).astype(np.float32)
        self.dirs = dirs

    def scan(self, scene, origin):
        """返回 open3d.geometry.PointCloud"""
        rays = np.hstack([
            np.full_like(self.dirs, origin, dtype=np.float32),
            self.dirs
        ])  # (N, 6)
        ans = scene.cast_rays(rays)
        t_hit = ans['t_hit'].numpy()          # (N,)
        mask = (t_hit < self.max_range) & (t_hit > 0)
        pts = origin + self.dirs[mask] * t_hit[mask, None]
        pc = o3d.geometry.PointCloud()
        pc.points = o3d.utility.Vector3dVector(pts)
        return pc

# ---------- 主流程 ----------
def real_time_moving_lidar2(glb_path,
                            start_position=(0, 0, 8),
                            duration=10.0,
                            speed=5.0,
                            fps=10,
                            quiet=False):
    print("\n=== 实时移动激光雷达仿真(优化版) ===")
    print(f"起始位置: {start_position}")
    print(f"持续时间: {duration}s  速度: {speed}u/s  FPS: {fps}")

    # 1. 加载模型
    mesh = o3d.io.read_triangle_mesh(glb_path)
    if not mesh.has_vertices():
        raise RuntimeError("模型为空")
    mesh.scale(0.01, center=(0, 0, 0))
    mesh.compute_vertex_normals()

    # 2. 构建加速结构
    scene = o3d.t.geometry.RaycastingScene()
    scene.add_triangles(o3d.t.geometry.TriangleMesh.from_legacy(mesh))

    # 3. 预计算位置
    frames = int(duration * fps)
    times = np.arange(0, frames) / fps
    positions = np.array([
        (start_position[0], start_position[1],
         start_position[2] + speed * t) for t in times
    ])

    # 4. 初始化 LiDAR
    lidar = FastLidar(h_fov=180, v_fov=45, h_res=0.1, v_res=0.1, max_range=30)

    # 5. 输出目录
    out_dir = "moving_lidar_output"
    os.makedirs(out_dir, exist_ok=True)

    # 6. 并行扫描 + I/O
    all_clouds, all_pos = [], []

    # 用于统计总耗时
    total_gen_time = 0.0

    def work(idx):
        nonlocal total_gen_time
        t0 = time.perf_counter()
        pc = lidar.scan(scene, positions[idx])
        t1 = time.perf_counter()
        total_gen_time += (t1 - t0)

        if len(pc.points) > 0:
            fname = os.path.join(out_dir, f"moving_lidar_frame_{idx:04d}.pcd")
            o3d.io.write_point_cloud(fname, pc, print_progress=False)
        return pc, positions[idx]

    with ThreadPoolExecutor(max_workers=os.cpu_count()) as pool:
        tasks = [pool.submit(work, i) for i in range(frames)]
        for fut in tqdm(tasks, disable=quiet, desc="Scanning"):
            pc, pos = fut.result()
            all_clouds.append(pc)
            all_pos.append(pos)

    # 7. 打印耗时统计
    print(f"\n点云生成总耗时: {total_gen_time:.3f} s")
    print(f"平均单帧生成耗时: {total_gen_time / frames:.3f} ms")

    # 8. 可视化
    # if not quiet:
    #     print("\n启动可视化...")
    #     visualize_moving_lidar_results(mesh, all_clouds, all_pos,
    #                                    start_position, speed, duration)

    print(f"\n✓ 完成! 共 {len(all_clouds)} 帧，保存在 {out_dir}/")

# ---------- 可视化 ----------
def visualize_moving_lidar_results(mesh, clouds, positions, start_pos, speed, duration):
    import open3d.visualization.gui as gui
    import open3d.visualization.rendering as rendering
    # … 与原函数一致，略 …
    pass



def visualize_moving_lidar_results(mesh, point_clouds, positions, start_pos, speed, duration):
    """可视化移动激光雷达的所有结果"""
    print("准备可视化数据...")
    
    # 创建可视化对象列表
    vis_objects = []
    
    # 添加原始模型（半透明）
    import copy
    mesh_copy = copy.deepcopy(mesh)
    mesh_copy.paint_uniform_color([0.7, 0.7, 0.7])
    vis_objects.append(mesh_copy)
    
    # 添加移动轨迹
    if len(positions) > 1:
        trajectory_points = []
        for pos in positions:
            trajectory_points.append(pos)
        
        # 创建轨迹线
        trajectory_lines = []
        for i in range(len(trajectory_points) - 1):
            trajectory_lines.append([i, i + 1])
        
        trajectory_line_set = o3d.geometry.LineSet()
        trajectory_line_set.points = o3d.utility.Vector3dVector(trajectory_points)
        trajectory_line_set.lines = o3d.utility.Vector2iVector(trajectory_lines)
        trajectory_line_set.colors = o3d.utility.Vector3dVector([[0, 1, 0]])  # 绿色轨迹
        vis_objects.append(trajectory_line_set)
    
    # 添加所有点云（使用不同颜色）
    import numpy as np
    for i, point_cloud in enumerate(point_clouds):
        if len(point_cloud.points) > 0:
            # 为每帧点云使用不同颜色
            hue = i / len(point_clouds)  # 0到1之间的色相
            # 简单的颜色映射：从蓝色到红色
            if hue < 0.5:
                color = [0, 0, 1 - 2 * hue]  # 蓝色到青色
            else:
                color = [2 * (hue - 0.5), 0, 0]  # 青色到红色
            
            colored_cloud = copy.deepcopy(point_cloud)
            colors = np.tile(color, (len(point_cloud.points), 1))
            colored_cloud.colors = o3d.utility.Vector3dVector(colors)
            vis_objects.append(colored_cloud)
    
    # 添加起始和结束位置标记
    start_marker = o3d.geometry.TriangleMesh.create_sphere(radius=0.3)
    start_marker.translate(start_pos)
    start_marker.paint_uniform_color([0, 1, 0])  # 绿色起始点
    vis_objects.append(start_marker)
    
    if len(positions) > 0:
        end_pos = positions[-1]
        end_marker = o3d.geometry.TriangleMesh.create_sphere(radius=0.3)
        end_marker.translate(end_pos)
        end_marker.paint_uniform_color([1, 0, 0])  # 红色结束点
        vis_objects.append(end_marker)
    
    # 显示可视化
    print("启动3D可视化窗口...")
    o3d.visualization.draw_geometries(
        vis_objects,
        window_name="移动激光雷达仿真结果",
        width=1400,
        height=900
    )


def multi_vehicle_lidar_simulation(glb_path, num_vehicles=20, vehicle_spacing=10, duration=20.0, speed=5.0, fps=10):
    """多车辆激光雷达仿真
    
    Args:
        glb_path (str): GLB文件路径
        num_vehicles (int): 车辆数量，默认20台
        vehicle_spacing (float): 车辆间距，默认10单位
        duration (float): 仿真持续时间（秒）
        speed (float): 车辆移动速度（单位/秒）
        fps (int): 帧率（每秒帧数）
    """
    print(f"\n=== 多车辆激光雷达仿真 ===")
    print(f"车辆数量: {num_vehicles}")
    print(f"车辆间距: {vehicle_spacing} 单位")
    print(f"持续时间: {duration}秒")
    print(f"移动速度: {speed} 单位/秒")
    print(f"帧率: {fps} FPS")
    
    try:
        # 加载GLB模型
        print("加载GLB模型...")
        import open3d as o3d
        mesh = o3d.io.read_triangle_mesh(glb_path)
        mesh.scale(0.01, center=(0, 0, 0))  # 缩放模型
        
        if len(mesh.vertices) == 0:
            print("模型加载失败或为空")
            return
        
        # 确保模型有法向量
        if not mesh.has_vertex_normals():
            mesh.compute_vertex_normals()
        
        # 计算车辆位置
        vehicle_positions = calculate_vehicle_positions(num_vehicles, vehicle_spacing)
        
        # 创建输出目录
        import os
        output_dir = "multi_vehicle_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 使用多进程并行处理
        import multiprocessing as mp
        from functools import partial
        
        print(f"\n启动多进程仿真...")
        print(f"CPU核心数: {mp.cpu_count()}")
        
        # 准备进程池
        num_processes = min(num_vehicles, mp.cpu_count())
        print(f"使用进程数: {num_processes}")
        
        # 创建进程池
        with mp.Pool(processes=num_processes) as pool:
            # 准备任务参数
            task_args = []
            for vehicle_id in range(num_vehicles):
                task_args.append({
                    'vehicle_id': vehicle_id,
                    'start_position': vehicle_positions[vehicle_id],
                    'glb_path': glb_path,  # 传递文件路径而非mesh对象
                    'duration': duration,
                    'speed': speed,
                    'fps': fps,
                    'output_dir': output_dir
                })
            
            # 执行并行仿真
            results = pool.map(simulate_single_vehicle, task_args)
        
        # 统计结果
        total_frames = 0
        total_points = 0
        successful_vehicles = 0
        
        for result in results:
            if result['success']:
                successful_vehicles += 1
                total_frames += result['frames']
                total_points += result['points']
        
        print(f"\n✓ 多车辆仿真完成!")
        print(f"✓ 成功仿真车辆: {successful_vehicles}/{num_vehicles}")
        print(f"✓ 总帧数: {total_frames}")
        print(f"✓ 总点数: {total_points:,}")
        print(f"✓ 所有文件保存在: {output_dir}/")
        
    except FileNotFoundError:
        print(f"GLB文件未找到: {glb_path}")
    except Exception as e:
        print(f"仿真过程中出错: {e}")
        import traceback
        traceback.print_exc()


def calculate_vehicle_positions(num_vehicles, spacing):
    """计算车辆初始位置"""
    positions = []
    
    # 计算每行车辆数（假设为正方形布局）
    vehicles_per_row = int(num_vehicles ** 0.5)
    if vehicles_per_row ** 2 < num_vehicles:
        vehicles_per_row += 1
    
    # 计算起始位置（使车辆居中，并确保在模型附近）
    start_x = -(vehicles_per_row - 1) * spacing / 2
    start_y = -(vehicles_per_row - 1) * spacing / 2
    
    vehicle_id = 0
    for row in range(vehicles_per_row):
        for col in range(vehicles_per_row):
            if vehicle_id >= num_vehicles:
                break
            
            x = start_x + col * spacing
            y = start_y + row * spacing
            z = 0.5  # 稍微抬高，避免与地面重叠
            
            # 对向行驶：偶数行正向，奇数行反向
            direction = 1 if row % 2 == 0 else -1
            
            positions.append({
                'position': (x, y, z),
                'direction': direction,
                'vehicle_id': vehicle_id
            })
            
            vehicle_id += 1
    
    return positions


def simulate_single_vehicle(args):
    """单个车辆仿真函数（用于多进程）"""
    vehicle_id = args['vehicle_id']
    start_position = args['start_position']
    glb_path = args['glb_path']
    duration = args['duration']
    speed = args['speed']
    fps = args['fps']
    output_dir = args['output_dir']
    
    try:
        # 在每个进程中独立加载GLB模型
        import open3d as o3d
        mesh = o3d.io.read_triangle_mesh(glb_path)
        mesh.scale(0.01, center=(0, 0, 0))  # 缩放模型
        
        if len(mesh.vertices) == 0:
            print(f"车辆 {vehicle_id:02d}: 模型加载失败")
            return {
                'vehicle_id': vehicle_id,
                'success': False,
                'frames': 0,
                'points': 0
            }
        
        # 确保模型有法向量
        if not mesh.has_vertex_normals():
            mesh.compute_vertex_normals()
        
        # 计算总帧数
        total_frames = int(duration * fps)
        frame_interval = 1.0 / fps
        
        # 获取车辆信息
        vehicle_pos = start_position['position']
        direction = start_position['direction']
        
        # 输出模型边界信息用于调试
        bbox = mesh.get_axis_aligned_bounding_box()
        print(f"车辆 {vehicle_id:02d}: 开始仿真 (方向: {direction:+d})")
        print(f"  模型边界: 最小{bbox.min_bound}, 最大{bbox.max_bound}")
        print(f"  模型中心: {bbox.get_center()}")
        
        frame_count = 0
        total_points = 0
        
        for frame in range(total_frames):
            # 计算当前时间
            current_time = frame * frame_interval
            
            # 计算当前位置（沿Z轴移动）
            current_z = vehicle_pos[2] + speed * current_time
            current_position = (vehicle_pos[0], vehicle_pos[1], current_z)
            
            # 计算激光雷达位置（车尾部，Y轴中心）
            # 假设车辆长度为2单位，激光雷达在车尾
            lidar_x = current_position[0]
            lidar_y = current_position[1] + (direction * 0.5)  # 车尾位置，减小偏移
            lidar_z = current_position[2] + 0.3  # 稍微抬高
            lidar_position = (lidar_x, lidar_y, lidar_z)
            
            # 激光雷达朝向（与车辆行驶方向相反，但更接近模型）
            lidar_direction = (0, -direction, -0.3)  # 向后下方扫描，减小角度
            
            # 输出调试信息（仅前几帧）
            if frame < 3:
                print(f"  帧 {frame}: 激光雷达位置 {lidar_position}, 朝向 {lidar_direction}")
            
            # 创建激光雷达
            lidar = LidarSimulator(
                position=lidar_position,
                direction=lidar_direction,
                horizontal_fov=180.0,   # 180度水平视角
                vertical_fov=90.0,      # 90度垂直视角
                horizontal_resolution=5.0,  # 降低分辨率提高性能
                vertical_resolution=5.0,
                max_range=50.0          # 增加最大距离
            )
            
            # 执行扫描
            point_cloud = lidar.simulate_lidar_scan(mesh)
            
            # 保存点云
            if len(point_cloud.points) > 0:
                filename = os.path.join(output_dir, f"vehicle_{vehicle_id:02d}_frame_{frame:04d}.pcd")
                lidar.save_point_cloud(point_cloud, filename)
                frame_count += 1
                total_points += len(point_cloud.points)
        
        print(f"车辆 {vehicle_id:02d}: 完成仿真 - {frame_count} 帧, {total_points:,} 个点")
        
        return {
            'vehicle_id': vehicle_id,
            'success': True,
            'frames': frame_count,
            'points': total_points
        }
        
    except Exception as e:
        print(f"车辆 {vehicle_id:02d}: 仿真失败 - {e}")
        import traceback
        traceback.print_exc()
        return {
            'vehicle_id': vehicle_id,
            'success': False,
            'frames': 0,
            'points': 0
        }


def main():
    """主函数 - 运行所有示例"""
    print("激光雷达点云仿真示例")
    print("=" * 50)
    
    # 运行示例（可以注释掉不需要的示例）
    # example_1_overhead_scan()
    # example_2_side_scan()
    # example_3_angled_scan()
    # example_4_high_density_scan()
    
    # GLB文件扫描示例 - 支持缩放配置
    print("\n=== 缩放配置示例 ===")
    
    # 示例1: 统一缩放 (放大2倍)
    print("示例1: 统一缩放 2倍")
    # load_and_scan_glb(scale=2.0)
    
    # 示例2: 详细缩放配置 (X轴放大1.5倍，Y轴缩小0.8倍，Z轴保持原大小)
    print("示例2: 详细缩放配置")
    # scale_config = {
    #     'scale_x': 1.5,
    #     'scale_y': 0.8,
    #     'scale_z': 1.0,
    #     'center': (0, 0, 0)
    # }
    # load_and_scan_glb(scale_config=scale_config)
    
    # 示例3: 原始大小
    print("示例3: 原始大小")
    # load_and_scan_glb()
    
    # 实时移动激光雷达仿真
    print("\n=== 实时移动激光雷达仿真 ===")
    glb_path = "C:/pyproject/trun_cloud/car.glb"  # 替换为您的GLB文件路径
    real_time_moving_lidar2(glb_path, start_position=(0, 0, 8), duration=1.0, speed=5.0, fps=1)
    
    # 多车辆激光雷达仿真
    print("\n=== 多车辆激光雷达仿真 ===")
    # glb_path = "C:/pyproject/trun_cloud/car.glb"  # 替换为您的GLB文件路径
    # multi_vehicle_lidar_simulation(glb_path, num_vehicles=20, vehicle_spacing=10, duration=20.0, speed=5.0, fps=10)


if __name__ == "__main__":
    main()
