"""
激光雷达仿真配置文件
"""

import numpy as np

# 激光雷达配置预设
LIDAR_PRESETS = {
    "overhead_scan": {
        "position": (0, 0, 15),
        "direction": (0, 0, -1),
        "horizontal_fov": 360.0,
        "vertical_fov": 60.0,
        "horizontal_resolution": 2.0,
        "vertical_resolution": 2.0,
        "max_range": 30.0,
        "description": "从上方向下的全方位扫描"
    },
    
    "side_scan": {
        "position": (-30, 0, 5),
        "direction": (1, 0, 0),
        "horizontal_fov": 120.0,
        "vertical_fov": 45.0,
        "horizontal_resolution": 1.0,
        "vertical_resolution": 1.0,
        "max_range": 50.0,
        "description": "从侧面的扫描"
    },
    
    "angled_scan": {
        "position": (20, 20, 10),
        "direction": (-1, -1, -0.5),
        "horizontal_fov": 90.0,
        "vertical_fov": 60.0,
        "horizontal_resolution": 1.5,
        "vertical_resolution": 1.5,
        "max_range": 40.0,
        "description": "倾斜角度扫描"
    },
    
    "high_density": {
        "position": (0, 0, 8),
        "direction": (0, 0, -1),
        "horizontal_fov": 180.0,
        "vertical_fov": 30.0,
        "horizontal_resolution": 0.5,
        "vertical_resolution": 0.5,
        "max_range": 25.0,
        "description": "高密度扫描"
    },
    
    "automotive": {
        "position": (0, 0, 2),
        "direction": (1, 0, 0),
        "horizontal_fov": 120.0,
        "vertical_fov": 26.8,
        "horizontal_resolution": 0.2,
        "vertical_resolution": 0.4,
        "max_range": 120.0,
        "description": "汽车激光雷达配置"
    },
    
    "drone": {
        "position": (0, 0, 50),
        "direction": (0, 0, -1),
        "horizontal_fov": 360.0,
        "vertical_fov": 30.0,
        "horizontal_resolution": 1.0,
        "vertical_resolution": 1.0,
        "max_range": 100.0,
        "description": "无人机激光雷达配置"
    },
    
    "indoor": {
        "position": (0, 0, 2.5),
        "direction": (1, 0, 0),
        "horizontal_fov": 270.0,
        "vertical_fov": 30.0,
        "horizontal_resolution": 0.25,
        "vertical_resolution": 0.5,
        "max_range": 30.0,
        "description": "室内扫描配置"
    },
    
    "quick_test": {
        "position": (0, 0, 5),
        "direction": (0, 0, -1),
        "horizontal_fov": 90.0,
        "vertical_fov": 30.0,
        "horizontal_resolution": 5.0,
        "vertical_resolution": 5.0,
        "max_range": 20.0,
        "description": "快速测试配置（低分辨率）"
    }
}

# 文件路径配置
FILE_PATHS = {
    "glb_model": "your_model.glb",  # 替换为您的GLB文件路径
    "output_dir": "output",         # 输出目录
    "pcd_filename": "lidar_scan.pcd"  # 默认PCD文件名
}

# 可视化配置
VISUALIZATION_CONFIG = {
    "window_name": "激光雷达点云仿真",
    "window_width": 1200,
    "window_height": 800,
    "point_size": 2.0,
    "line_width": 2.0,
    "background_color": [0.1, 0.1, 0.1],  # 深灰色背景
    "mesh_color": [0.7, 0.7, 0.7],        # 模型颜色
    "point_cloud_color": [0.8, 0.2, 0.2], # 点云颜色（红色）
    "lidar_marker_color": [0, 1, 0],      # 激光雷达标记颜色（绿色）
    "direction_line_color": [0, 1, 0],    # 方向线颜色（绿色）
    "lidar_marker_radius": 0.5,           # 激光雷达标记半径
    "direction_line_length": 5.0          # 方向线长度
}

# 性能配置
PERFORMANCE_CONFIG = {
    "max_rays_warning": 100000,  # 射线数量警告阈值
    "max_points_warning": 500000,  # 点数量警告阈值
    "enable_progress_bar": True,   # 是否显示进度条
    "parallel_processing": True,   # 是否启用并行处理
    "memory_limit_gb": 8          # 内存限制（GB）
}

# 输出配置
OUTPUT_CONFIG = {
    "save_pcd": True,              # 是否保存PCD文件
    "save_ply": False,             # 是否保存PLY文件
    "save_xyz": False,             # 是否保存XYZ文件
    "compress_pcd": True,          # 是否压缩PCD文件
    "include_colors": True,        # 是否包含颜色信息
    "include_normals": False,      # 是否包含法向量信息
    "precision": 6                 # 坐标精度（小数位数）
}

# 场景生成配置
SCENE_CONFIG = {
    "ground_size": 50.0,           # 地面大小
    "ground_height": 0.1,          # 地面厚度
    "building_count": 3,           # 建筑物数量
    "tree_count": 2,               # 树木数量
    "random_seed": 42,             # 随机种子
    "building_height_range": (5, 15),  # 建筑物高度范围
    "building_size_range": (3, 8),     # 建筑物大小范围
}


def get_preset_config(preset_name: str) -> dict:
    """获取预设配置"""
    if preset_name not in LIDAR_PRESETS:
        available_presets = list(LIDAR_PRESETS.keys())
        raise ValueError(f"未知的预设配置: {preset_name}. 可用配置: {available_presets}")
    
    return LIDAR_PRESETS[preset_name].copy()


def list_presets():
    """列出所有可用的预设配置"""
    print("可用的激光雷达预设配置:")
    print("-" * 50)
    for name, config in LIDAR_PRESETS.items():
        print(f"{name:15} - {config['description']}")
        print(f"{'':15}   位置: {config['position']}")
        print(f"{'':15}   方向: {config['direction']}")
        print(f"{'':15}   FOV: H{config['horizontal_fov']}° × V{config['vertical_fov']}°")
        print(f"{'':15}   分辨率: {config['horizontal_resolution']}° × {config['vertical_resolution']}°")
        print(f"{'':15}   最大距离: {config['max_range']}m")
        print()


def create_custom_config(position, direction, horizontal_fov=120.0, vertical_fov=30.0,
                        horizontal_resolution=1.0, vertical_resolution=1.0, max_range=50.0):
    """创建自定义配置"""
    return {
        "position": position,
        "direction": direction,
        "horizontal_fov": horizontal_fov,
        "vertical_fov": vertical_fov,
        "horizontal_resolution": horizontal_resolution,
        "vertical_resolution": vertical_resolution,
        "max_range": max_range,
        "description": "自定义配置"
    }


def estimate_performance(config: dict) -> dict:
    """估算性能指标"""
    h_angles = int(config["horizontal_fov"] / config["horizontal_resolution"])
    v_angles = int(config["vertical_fov"] / config["vertical_resolution"])
    total_rays = h_angles * v_angles
    
    # 估算内存使用（MB）
    estimated_memory = total_rays * 0.0001  # 粗略估算
    
    # 估算处理时间（秒）
    estimated_time = total_rays * 0.00001  # 粗略估算
    
    return {
        "total_rays": total_rays,
        "estimated_memory_mb": estimated_memory,
        "estimated_time_seconds": estimated_time,
        "performance_level": "高" if total_rays < 10000 else "中" if total_rays < 50000 else "低"
    }


def validate_config(config: dict) -> bool:
    """验证配置参数"""
    required_keys = ["position", "direction", "horizontal_fov", "vertical_fov",
                    "horizontal_resolution", "vertical_resolution", "max_range"]
    
    for key in required_keys:
        if key not in config:
            print(f"错误: 缺少必需的配置参数: {key}")
            return False
    
    # 验证数值范围
    if config["horizontal_fov"] <= 0 or config["horizontal_fov"] > 360:
        print("错误: horizontal_fov 必须在 (0, 360] 范围内")
        return False
    
    if config["vertical_fov"] <= 0 or config["vertical_fov"] > 180:
        print("错误: vertical_fov 必须在 (0, 180] 范围内")
        return False
    
    if config["horizontal_resolution"] <= 0:
        print("错误: horizontal_resolution 必须大于 0")
        return False
    
    if config["vertical_resolution"] <= 0:
        print("错误: vertical_resolution 必须大于 0")
        return False
    
    if config["max_range"] <= 0:
        print("错误: max_range 必须大于 0")
        return False
    
    # 验证方向向量
    direction = np.array(config["direction"])
    if np.linalg.norm(direction) == 0:
        print("错误: direction 不能是零向量")
        return False
    
    return True


if __name__ == "__main__":
    # 显示所有预设配置
    list_presets()
    
    # 显示性能估算示例
    print("\n性能估算示例:")
    print("-" * 30)
    for name in ["quick_test", "automotive", "high_density"]:
        config = get_preset_config(name)
        perf = estimate_performance(config)
        print(f"{name}: {perf['total_rays']} 射线, "
              f"性能等级: {perf['performance_level']}, "
              f"预计时间: {perf['estimated_time_seconds']:.2f}s")
